[{"C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\AuthSection.js": "3", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\CampaignSection.js": "4", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\FacebookSection.js": "5", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\ApiTestingSection.js": "6", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\contexts\\AuthContext.js": "7", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\services\\api.js": "8", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\FacebookConnectionSection.js": "9", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\contexts\\FacebookContext.js": "10", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\TargetingDisplay.js": "11", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\CreativeDisplay.js": "12", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\CampaignWizard.js": "13", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\LeadFormsSection.js": "14"}, {"size": 232, "mtime": 1750870627179, "results": "15", "hashOfConfig": "16"}, {"size": 2303, "mtime": 1750935875780, "results": "17", "hashOfConfig": "16"}, {"size": 10186, "mtime": 1750904956981, "results": "18", "hashOfConfig": "16"}, {"size": 16860, "mtime": 1750925531848, "results": "19", "hashOfConfig": "16"}, {"size": 7324, "mtime": 1750904956983, "results": "20", "hashOfConfig": "16"}, {"size": 8243, "mtime": 1750873838649, "results": "21", "hashOfConfig": "16"}, {"size": 3438, "mtime": 1750870723227, "results": "22", "hashOfConfig": "16"}, {"size": 4416, "mtime": 1750935490037, "results": "23", "hashOfConfig": "16"}, {"size": 4531, "mtime": 1750906642416, "results": "24", "hashOfConfig": "16"}, {"size": 4881, "mtime": 1750906513515, "results": "25", "hashOfConfig": "16"}, {"size": 15521, "mtime": 1750913282515, "results": "26", "hashOfConfig": "16"}, {"size": 11191, "mtime": 1750915070114, "results": "27", "hashOfConfig": "16"}, {"size": 28034, "mtime": 1750936563109, "results": "28", "hashOfConfig": "16"}, {"size": 21576, "mtime": 1750936511226, "results": "29", "hashOfConfig": "16"}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1klh60u", {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\AuthSection.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\CampaignSection.js", ["72", "73"], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\FacebookSection.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\ApiTestingSection.js", ["74", "75"], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\contexts\\AuthContext.js", ["76"], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\services\\api.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\FacebookConnectionSection.js", ["77", "78"], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\contexts\\FacebookContext.js", ["79"], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\TargetingDisplay.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\CreativeDisplay.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\CampaignWizard.js", ["80"], ["81", "82"], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\LeadFormsSection.js", [], ["83", "84"], {"ruleId": "85", "severity": 1, "message": "86", "line": 65, "column": 6, "nodeType": "87", "endLine": 65, "endColumn": 23, "suggestions": "88"}, {"ruleId": "85", "severity": 1, "message": "86", "line": 76, "column": 6, "nodeType": "87", "endLine": 76, "endColumn": 46, "suggestions": "89"}, {"ruleId": "90", "severity": 1, "message": "91", "line": 8, "column": 28, "nodeType": "92", "messageId": "93", "endLine": 8, "endColumn": 32}, {"ruleId": "85", "severity": 1, "message": "94", "line": 22, "column": 6, "nodeType": "87", "endLine": 22, "endColumn": 23, "suggestions": "95"}, {"ruleId": "85", "severity": 1, "message": "96", "line": 22, "column": 6, "nodeType": "87", "endLine": 22, "endColumn": 8, "suggestions": "97"}, {"ruleId": "90", "severity": 1, "message": "98", "line": 5, "column": 20, "nodeType": "92", "messageId": "93", "endLine": 5, "endColumn": 25}, {"ruleId": "85", "severity": 1, "message": "99", "line": 25, "column": 6, "nodeType": "87", "endLine": 25, "endColumn": 32, "suggestions": "100"}, {"ruleId": "85", "severity": 1, "message": "101", "line": 24, "column": 6, "nodeType": "87", "endLine": 24, "endColumn": 8, "suggestions": "102"}, {"ruleId": "90", "severity": 1, "message": "103", "line": 23, "column": 10, "nodeType": "92", "messageId": "93", "endLine": 23, "endColumn": 27}, {"ruleId": "85", "severity": 1, "message": "104", "line": 87, "column": 6, "nodeType": "87", "endLine": 87, "endColumn": 8, "suggestions": "105", "suppressions": "106"}, {"ruleId": "85", "severity": 1, "message": "107", "line": 94, "column": 6, "nodeType": "87", "endLine": 94, "endColumn": 23, "suggestions": "108", "suppressions": "109"}, {"ruleId": "85", "severity": 1, "message": "99", "line": 108, "column": 6, "nodeType": "87", "endLine": 108, "endColumn": 8, "suggestions": "110", "suppressions": "111"}, {"ruleId": "85", "severity": 1, "message": "107", "line": 115, "column": 6, "nodeType": "87", "endLine": 115, "endColumn": 23, "suggestions": "112", "suppressions": "113"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadCampaigns'. Either include it or remove the dependency array.", "ArrayExpression", ["114"], ["115"], "no-unused-vars", "'user' is assigned a value but never used.", "Identifier", "unusedVar", "React Hook useEffect has missing dependencies: 'checkServerHealth' and 'loadUserProfile'. Either include them or remove the dependency array.", ["116"], "React Hook useEffect has a missing dependency: 'checkAuthStatus'. Either include it or remove the dependency array.", ["117"], "'Users' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadAdAccounts'. Either include it or remove the dependency array.", ["118"], "React Hook useEffect has missing dependencies: 'checkFacebookAuth' and 'handleOAuthCallback'. Either include them or remove the dependency array.", ["119"], "'leadFormTemplates' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadFormData'. Either include it or remove the dependency array.", ["120"], ["121"], "React Hook useEffect has a missing dependency: 'loadLeadForms'. Either include it or remove the dependency array.", ["122"], ["123"], ["124"], ["125"], ["126"], ["127"], {"desc": "128", "fix": "129"}, {"desc": "130", "fix": "131"}, {"desc": "132", "fix": "133"}, {"desc": "134", "fix": "135"}, {"desc": "136", "fix": "137"}, {"desc": "138", "fix": "139"}, {"desc": "140", "fix": "141"}, {"kind": "142", "justification": "143"}, {"desc": "144", "fix": "145"}, {"kind": "142", "justification": "143"}, {"desc": "146", "fix": "147"}, {"kind": "142", "justification": "143"}, {"desc": "144", "fix": "148"}, {"kind": "142", "justification": "143"}, "Update the dependencies array to be: [loadCampaigns, selectedAccount]", {"range": "149", "text": "150"}, "Update the dependencies array to be: [activeTab, selectedAccount, loadedData, loadCampaigns]", {"range": "151", "text": "152"}, "Update the dependencies array to be: [checkServerHealth, isAuthenticated, loadUserProfile]", {"range": "153", "text": "154"}, "Update the dependencies array to be: [checkAuthStatus]", {"range": "155", "text": "156"}, "Update the dependencies array to be: [isConnected, accessToken, loadAdAccounts]", {"range": "157", "text": "158"}, "Update the dependencies array to be: [checkFacebookAuth, handleOAuthCallback]", {"range": "159", "text": "160"}, "Update the dependencies array to be: [loadFormData]", {"range": "161", "text": "162"}, "directive", "", "Update the dependencies array to be: [loadLeadForms, selectedAccount]", {"range": "163", "text": "164"}, "Update the dependencies array to be: [loadAdAccounts]", {"range": "165", "text": "166"}, {"range": "167", "text": "164"}, [2408, 2425], "[load<PERSON><PERSON>ai<PERSON><PERSON>, selectedAccount]", [2833, 2873], "[activeTab, selectedAccount, loadedData, loadCampaigns]", [722, 739], "[checkServerHealth, isAuthenticated, loadUserProfile]", [636, 638], "[checkAuthStatus]", [724, 750], "[isConnected, accessToken, loadAdAccounts]", [762, 764], "[checkFacebookAuth, handleOAuthCallback]", [2300, 2302], "[loadFormData]", [2447, 2464], "[loadLeadForms, selectedAccount]", [2972, 2974], "[loadAdAccounts]", [3119, 3136]]