[{"C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\AuthSection.js": "3", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\CampaignSection.js": "4", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\FacebookSection.js": "5", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\ApiTestingSection.js": "6", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\contexts\\AuthContext.js": "7", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\services\\api.js": "8", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\FacebookConnectionSection.js": "9", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\contexts\\FacebookContext.js": "10", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\TargetingDisplay.js": "11", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\CreativeDisplay.js": "12", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\CampaignWizard.js": "13", "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\LeadFormsSection.js": "14"}, {"size": 232, "mtime": 1750870627179, "results": "15", "hashOfConfig": "16"}, {"size": 2303, "mtime": 1750935875780, "results": "17", "hashOfConfig": "16"}, {"size": 10186, "mtime": 1750904956981, "results": "18", "hashOfConfig": "16"}, {"size": 16860, "mtime": 1750925531848, "results": "19", "hashOfConfig": "16"}, {"size": 7324, "mtime": 1750904956983, "results": "20", "hashOfConfig": "16"}, {"size": 8243, "mtime": 1750873838649, "results": "21", "hashOfConfig": "16"}, {"size": 3438, "mtime": 1750870723227, "results": "22", "hashOfConfig": "16"}, {"size": 4416, "mtime": 1750935490037, "results": "23", "hashOfConfig": "16"}, {"size": 4531, "mtime": 1750906642416, "results": "24", "hashOfConfig": "16"}, {"size": 4881, "mtime": 1750906513515, "results": "25", "hashOfConfig": "16"}, {"size": 15521, "mtime": 1750913282515, "results": "26", "hashOfConfig": "16"}, {"size": 11191, "mtime": 1750915070114, "results": "27", "hashOfConfig": "16"}, {"size": 28018, "mtime": 1750936296229, "results": "28", "hashOfConfig": "16"}, {"size": 21564, "mtime": 1750936362339, "results": "29", "hashOfConfig": "16"}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1klh60u", {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\AuthSection.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\CampaignSection.js", ["72", "73"], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\FacebookSection.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\ApiTestingSection.js", ["74", "75"], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\contexts\\AuthContext.js", ["76"], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\services\\api.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\FacebookConnectionSection.js", ["77", "78"], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\contexts\\FacebookContext.js", ["79"], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\TargetingDisplay.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\CreativeDisplay.js", [], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\CampaignWizard.js", ["80", "81", "82", "83"], [], "C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-frontend\\src\\components\\LeadFormsSection.js", ["84", "85"], [], {"ruleId": "86", "severity": 1, "message": "87", "line": 65, "column": 6, "nodeType": "88", "endLine": 65, "endColumn": 23, "suggestions": "89"}, {"ruleId": "86", "severity": 1, "message": "87", "line": 76, "column": 6, "nodeType": "88", "endLine": 76, "endColumn": 46, "suggestions": "90"}, {"ruleId": "91", "severity": 1, "message": "92", "line": 8, "column": 28, "nodeType": "93", "messageId": "94", "endLine": 8, "endColumn": 32}, {"ruleId": "86", "severity": 1, "message": "95", "line": 22, "column": 6, "nodeType": "88", "endLine": 22, "endColumn": 23, "suggestions": "96"}, {"ruleId": "86", "severity": 1, "message": "97", "line": 22, "column": 6, "nodeType": "88", "endLine": 22, "endColumn": 8, "suggestions": "98"}, {"ruleId": "91", "severity": 1, "message": "99", "line": 5, "column": 20, "nodeType": "93", "messageId": "94", "endLine": 5, "endColumn": 25}, {"ruleId": "86", "severity": 1, "message": "100", "line": 25, "column": 6, "nodeType": "88", "endLine": 25, "endColumn": 32, "suggestions": "101"}, {"ruleId": "86", "severity": 1, "message": "102", "line": 24, "column": 6, "nodeType": "88", "endLine": 24, "endColumn": 8, "suggestions": "103"}, {"ruleId": "91", "severity": 1, "message": "104", "line": 23, "column": 10, "nodeType": "93", "messageId": "94", "endLine": 23, "endColumn": 27}, {"ruleId": "105", "severity": 1, "message": "106", "line": 86, "column": 7, "nodeType": "93", "messageId": "107", "endLine": 86, "endColumn": 19}, {"ruleId": "105", "severity": 1, "message": "108", "line": 92, "column": 24, "nodeType": "93", "messageId": "107", "endLine": 92, "endColumn": 37}, {"ruleId": "86", "severity": 1, "message": "109", "line": 134, "column": 6, "nodeType": "88", "endLine": 134, "endColumn": 23, "suggestions": "110"}, {"ruleId": "105", "severity": 1, "message": "111", "line": 53, "column": 7, "nodeType": "93", "messageId": "107", "endLine": 53, "endColumn": 21}, {"ruleId": "105", "severity": 1, "message": "108", "line": 59, "column": 24, "nodeType": "93", "messageId": "107", "endLine": 59, "endColumn": 37}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadCampaigns'. Either include it or remove the dependency array.", "ArrayExpression", ["112"], ["113"], "no-unused-vars", "'user' is assigned a value but never used.", "Identifier", "unusedVar", "React Hook useEffect has missing dependencies: 'checkServerHealth' and 'loadUserProfile'. Either include them or remove the dependency array.", ["114"], "React Hook useEffect has a missing dependency: 'checkAuthStatus'. Either include it or remove the dependency array.", ["115"], "'Users' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadAdAccounts'. Either include it or remove the dependency array.", ["116"], "React Hook useEffect has missing dependencies: 'checkFacebookAuth' and 'handleOAuthCallback'. Either include them or remove the dependency array.", ["117"], "'leadFormTemplates' is assigned a value but never used.", "no-use-before-define", "'loadFormData' was used before it was defined.", "usedBeforeDefined", "'loadLeadForms' was used before it was defined.", "React Hook useCallback has missing dependencies: 'loadLeadForms' and 'setValue'. Either include them or remove the dependency array.", ["118"], "'loadAdAccounts' was used before it was defined.", {"desc": "119", "fix": "120"}, {"desc": "121", "fix": "122"}, {"desc": "123", "fix": "124"}, {"desc": "125", "fix": "126"}, {"desc": "127", "fix": "128"}, {"desc": "129", "fix": "130"}, {"desc": "131", "fix": "132"}, "Update the dependencies array to be: [loadCampaigns, selectedAccount]", {"range": "133", "text": "134"}, "Update the dependencies array to be: [activeTab, selectedAccount, loadedData, loadCampaigns]", {"range": "135", "text": "136"}, "Update the dependencies array to be: [checkServerHealth, isAuthenticated, loadUserProfile]", {"range": "137", "text": "138"}, "Update the dependencies array to be: [checkAuthStatus]", {"range": "139", "text": "140"}, "Update the dependencies array to be: [isConnected, accessToken, loadAdAccounts]", {"range": "141", "text": "142"}, "Update the dependencies array to be: [checkFacebookAuth, handleOAuthCallback]", {"range": "143", "text": "144"}, "Update the dependencies array to be: [loadLeadForms, selectedAccount, setValue]", {"range": "145", "text": "146"}, [2408, 2425], "[load<PERSON><PERSON>ai<PERSON><PERSON>, selectedAccount]", [2833, 2873], "[activeTab, selectedAccount, loadedData, loadCampaigns]", [722, 739], "[checkServerHealth, isAuthenticated, loadUserProfile]", [636, 638], "[checkAuthStatus]", [724, 750], "[isConnected, accessToken, loadAdAccounts]", [762, 764], "[checkFacebookAuth, handleOAuthCallback]", [3904, 3921], "[loadLeadForms, selectedAccount, setValue]"]