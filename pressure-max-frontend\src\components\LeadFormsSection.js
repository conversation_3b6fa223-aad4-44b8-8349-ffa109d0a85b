import React, { useState, useEffect, useCallback } from 'react';
import { useForm } from 'react-hook-form';
import toast from 'react-hot-toast';
import { 
  FileText, 
  Plus, 
  Eye, 
  Edit, 
  Trash2, 
  Users, 
  Calendar,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react';
import api from '../services/api';

const LeadFormsSection = () => {
  const [leadForms, setLeadForms] = useState([]);
  const [loading, setLoading] = useState(false);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [leadFormTemplates, setLeadFormTemplates] = useState({});
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [adAccounts, setAdAccounts] = useState([]);
  const [facebookPages, setFacebookPages] = useState([]);
  const [selectedAccount, setSelectedAccount] = useState('');

  const { register, handleSubmit, watch, setValue, reset, formState: { errors } } = useForm({
    defaultValues: {
      pageId: '',
      name: '',
      privacyPolicyUrl: '',
      questions: [],
      thankYouPage: {
        title: 'Thank you!',
        body: 'We\'ll be in touch soon.'
      },
      contextCard: {
        title: '',
        content: ''
      },
      followUpActionUrl: '',
      locale: 'en_US'
    }
  });

  const watchedPageId = watch('pageId');

  useEffect(() => {
    loadAdAccounts();
    loadFacebookPages();
    loadLeadFormTemplates();
  }, [loadAdAccounts]);

  useEffect(() => {
    if (selectedAccount) {
      loadLeadForms();
    }
  }, [selectedAccount, loadLeadForms]);

  useEffect(() => {
    // Auto-select first page if available
    if (facebookPages && facebookPages.length > 0 && !watchedPageId) {
      setValue('pageId', facebookPages[0].id);
    }
  }, [facebookPages, setValue, watchedPageId]);

  const loadLeadForms = useCallback(async () => {
    if (!selectedAccount) return;
    
    try {
      setLoading(true);
      const response = await api.get(`/facebook/leadforms/${selectedAccount}`);
      setLeadForms(response.data || []);
    } catch (error) {
      console.error('Error loading lead forms:', error);
      toast.error('Failed to load lead forms');
      setLeadForms([]);
    } finally {
      setLoading(false);
    }
  }, [selectedAccount]);

  const loadAdAccounts = useCallback(async () => {
    try {
      const response = await api.get('/facebook/ad-accounts');
      setAdAccounts(response.data || []);

      // Auto-select first account if available
      if (response.data && response.data.length > 0 && !selectedAccount) {
        setSelectedAccount(response.data[0].id);
      }
    } catch (error) {
      console.error('Error loading ad accounts:', error);
      setAdAccounts([]);
    }
  }, [selectedAccount]);

  const loadFacebookPages = async () => {
    try {
      const response = await api.get('/facebook/pages');
      setFacebookPages(response.data || []);
    } catch (error) {
      console.error('Error loading Facebook pages:', error);
      setFacebookPages([]);
    }
  };

  const loadLeadFormTemplates = async () => {
    try {
      const response = await api.get('/facebook/leadform-templates');
      setLeadFormTemplates(response.data || {});
    } catch (error) {
      console.error('Error loading lead form templates:', error);
      setLeadFormTemplates({
        questionTypes: [],
        templates: []
      });
    }
  };

  const onCreateLeadForm = async (data) => {
    try {
      setLoading(true);
      
      // Prepare questions array
      const questions = data.questions.map(q => ({
        type: q.type,
        key: q.key || q.type.toLowerCase(),
        label: q.label || leadFormTemplates.questionTypes?.find(qt => qt.value === q.type)?.label || q.type
      }));

      const leadFormData = {
        pageId: data.pageId,
        name: data.name,
        privacyPolicyUrl: data.privacyPolicyUrl,
        questions: questions,
        thankYouPage: data.thankYouPage,
        contextCard: data.contextCard.title ? data.contextCard : null,
        followUpActionUrl: data.followUpActionUrl || null,
        locale: data.locale
      };

      await api.post('/facebook/leadforms', leadFormData);
      toast.success('Lead form created successfully!');

      // Reset form and reload lead forms
      reset();
      setShowCreateForm(false);
      setSelectedTemplate(null);
      loadLeadForms();
    } catch (error) {
      console.error('Error creating lead form:', error);
      toast.error(error.response?.data?.error || 'Failed to create lead form');
    } finally {
      setLoading(false);
    }
  };

  const applyTemplate = (template) => {
    setSelectedTemplate(template);
    setValue('name', template.name);
    setValue('questions', template.questions);
    toast.success(`Applied template: ${template.name}`);
  };

  const addQuestion = () => {
    const currentQuestions = watch('questions') || [];
    setValue('questions', [...currentQuestions, { type: 'FIRST_NAME', label: 'First Name' }]);
  };

  const removeQuestion = (index) => {
    const currentQuestions = watch('questions') || [];
    const newQuestions = currentQuestions.filter((_, i) => i !== index);
    setValue('questions', newQuestions);
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'ACTIVE':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'PAUSED':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <AlertCircle className="h-4 w-4 text-yellow-500" />;
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString();
  };

  if (loading && leadForms.length === 0) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-gray-500">Loading lead forms...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-bold text-gray-900">Facebook Lead Forms</h2>
          <button
            onClick={() => setShowCreateForm(!showCreateForm)}
            className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            disabled={!selectedAccount}
          >
            <Plus className="h-4 w-4 mr-2" />
            {showCreateForm ? 'Cancel' : 'Create Lead Form'}
          </button>
        </div>

        {/* Ad Account Selector */}
        <div className="flex items-center space-x-4">
          <label className="text-sm font-medium text-gray-700">Ad Account:</label>
          <select
            value={selectedAccount}
            onChange={(e) => setSelectedAccount(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">Select an ad account</option>
            {adAccounts.map(account => (
              <option key={account.id} value={account.id}>
                {account.name} ({account.id})
              </option>
            ))}
          </select>
          <div className="flex items-center text-sm text-gray-600">
            <FileText className="h-4 w-4 mr-1" />
            {leadForms.length} Lead Forms
          </div>
        </div>
      </div>

      {!selectedAccount && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex">
            <AlertCircle className="h-5 w-5 text-yellow-400 mr-2 mt-0.5" />
            <div>
              <h4 className="text-sm font-medium text-yellow-800">No Ad Account Selected</h4>
              <p className="text-sm text-yellow-700">
                Please select an ad account to view and manage lead forms.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Create Form */}
      {showCreateForm && selectedAccount && (
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h4 className="text-lg font-semibold text-gray-900 mb-4">Create New Lead Form</h4>
          
          {/* Templates */}
          {leadFormTemplates.templates && leadFormTemplates.templates.length > 0 && (
            <div className="mb-6">
              <h5 className="text-sm font-medium text-gray-700 mb-3">Quick Start Templates</h5>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
                {leadFormTemplates.templates.map((template, index) => (
                  <button
                    key={index}
                    type="button"
                    onClick={() => applyTemplate(template)}
                    className={`p-3 border rounded-lg text-left hover:bg-blue-50 hover:border-blue-300 transition-colors ${
                      selectedTemplate?.name === template.name 
                        ? 'border-blue-500 bg-blue-50' 
                        : 'border-gray-200'
                    }`}
                  >
                    <div className="font-medium text-sm text-gray-900">{template.name}</div>
                    <div className="text-xs text-gray-500 mt-1">{template.description}</div>
                    <div className="text-xs text-gray-400 mt-1">
                      {template.questions.length} questions
                    </div>
                  </button>
                ))}
              </div>
            </div>
          )}

          <form onSubmit={handleSubmit(onCreateLeadForm)} className="space-y-4">
            {/* Basic Info */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Facebook Page *
                </label>
                <select
                  {...register('pageId', { required: 'Facebook page is required' })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {facebookPages?.map(page => (
                    <option key={page.id} value={page.id}>
                      {page.name}
                    </option>
                  )) || []}
                </select>
                {errors.pageId && (
                  <p className="mt-1 text-sm text-red-600">{errors.pageId.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Lead Form Name *
                </label>
                <input
                  {...register('name', { required: 'Lead form name is required' })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter lead form name"
                />
                {errors.name && (
                  <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
                )}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Privacy Policy URL *
              </label>
              <input
                {...register('privacyPolicyUrl', { 
                  required: 'Privacy policy URL is required',
                  pattern: {
                    value: /^https?:\/\/.+/,
                    message: 'Please enter a valid URL'
                  }
                })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="https://example.com/privacy-policy"
              />
              {errors.privacyPolicyUrl && (
                <p className="mt-1 text-sm text-red-600">{errors.privacyPolicyUrl.message}</p>
              )}
            </div>

            {/* Questions Section */}
            <div>
              <div className="flex justify-between items-center mb-3">
                <label className="block text-sm font-medium text-gray-700">
                  Questions *
                </label>
                <button
                  type="button"
                  onClick={addQuestion}
                  className="flex items-center px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  <Plus className="h-3 w-3 mr-1" />
                  Add Question
                </button>
              </div>

              <div className="space-y-3">
                {(watch('questions') || []).map((question, index) => (
                  <div key={index} className="flex items-center space-x-3 p-3 border border-gray-200 rounded-md">
                    <div className="flex-1">
                      <select
                        {...register(`questions.${index}.type`, { required: 'Question type is required' })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        {leadFormTemplates.questionTypes?.map(type => (
                          <option key={type.value} value={type.value}>
                            {type.label} {type.required ? '*' : ''}
                          </option>
                        )) || []}
                      </select>
                    </div>
                    <button
                      type="button"
                      onClick={() => removeQuestion(index)}
                      className="p-2 text-red-600 hover:text-red-800"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                ))}

                {(!watch('questions') || watch('questions').length === 0) && (
                  <div className="text-center py-4 text-gray-500 border-2 border-dashed border-gray-300 rounded-md">
                    No questions added yet. Click "Add Question" to get started.
                  </div>
                )}
              </div>
            </div>

            {/* Thank You Page */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Thank You Title
                </label>
                <input
                  {...register('thankYouPage.title')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Thank you!"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Thank You Message
                </label>
                <input
                  {...register('thankYouPage.body')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="We'll be in touch soon."
                />
              </div>
            </div>

            {/* Optional Fields */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Context Card Title
                </label>
                <input
                  {...register('contextCard.title')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Optional context title"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Follow-up Action URL
                </label>
                <input
                  {...register('followUpActionUrl')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="https://example.com/thank-you"
                />
              </div>
            </div>

            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={() => {
                  setShowCreateForm(false);
                  setSelectedTemplate(null);
                  reset();
                }}
                className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={loading}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
              >
                {loading ? 'Creating...' : 'Create Lead Form'}
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Lead Forms List */}
      {selectedAccount && (
        <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
          {leadForms.length === 0 ? (
            <div className="p-8 text-center">
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Lead Forms Found</h3>
              <p className="text-gray-500 mb-4">
                Create your first lead form to start collecting leads from your Facebook ads.
              </p>
              <button
                onClick={() => setShowCreateForm(true)}
                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                <Plus className="h-4 w-4 mr-2" />
                Create Lead Form
              </button>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Lead Form
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Leads
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Created
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {leadForms.map((form) => (
                    <tr key={form.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{form.name}</div>
                          <div className="text-sm text-gray-500">ID: {form.id}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          {getStatusIcon(form.status)}
                          <span className="ml-2 text-sm text-gray-900">{form.status}</span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center text-sm text-gray-900">
                          <Users className="h-4 w-4 mr-1" />
                          {form.leads_count || 0}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center text-sm text-gray-900">
                          <Calendar className="h-4 w-4 mr-1" />
                          {formatDate(form.created_time)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          <button className="text-blue-600 hover:text-blue-900">
                            <Eye className="h-4 w-4" />
                          </button>
                          <button className="text-gray-600 hover:text-gray-900">
                            <Edit className="h-4 w-4" />
                          </button>
                          <button className="text-red-600 hover:text-red-900">
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default LeadFormsSection;
