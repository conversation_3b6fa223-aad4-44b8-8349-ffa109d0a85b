{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FB API SCRAPER\\\\pressure-max-frontend\\\\src\\\\components\\\\LeadFormsSection.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useForm } from 'react-hook-form';\nimport toast from 'react-hot-toast';\nimport { FileText, Plus, Eye, Edit, Trash2, Users, Calendar, CheckCircle, XCircle, AlertCircle } from 'lucide-react';\nimport api from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LeadFormsSection = () => {\n  _s();\n  const [leadForms, setLeadForms] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [showCreateForm, setShowCreateForm] = useState(false);\n  const [leadFormTemplates, setLeadFormTemplates] = useState({});\n  const [selectedTemplate, setSelectedTemplate] = useState(null);\n  const [adAccounts, setAdAccounts] = useState([]);\n  const [facebookPages, setFacebookPages] = useState([]);\n  const [selectedAccount, setSelectedAccount] = useState('');\n  const {\n    register,\n    handleSubmit,\n    watch,\n    setValue,\n    reset,\n    formState: {\n      errors\n    }\n  } = useForm({\n    defaultValues: {\n      pageId: '',\n      name: '',\n      privacyPolicyUrl: '',\n      questions: [],\n      thankYouPage: {\n        title: 'Thank you!',\n        body: 'We\\'ll be in touch soon.'\n      },\n      contextCard: {\n        title: '',\n        content: ''\n      },\n      followUpActionUrl: '',\n      locale: 'en_US'\n    }\n  });\n  const watchedPageId = watch('pageId');\n  useEffect(() => {\n    loadAdAccounts();\n    loadFacebookPages();\n    loadLeadFormTemplates();\n  }, []);\n  useEffect(() => {\n    if (selectedAccount) {\n      loadLeadForms();\n    }\n  }, [selectedAccount]);\n  useEffect(() => {\n    // Auto-select first page if available\n    if (facebookPages && facebookPages.length > 0 && !watchedPageId) {\n      setValue('pageId', facebookPages[0].id);\n    }\n  }, [facebookPages, setValue, watchedPageId]);\n  const loadLeadForms = async () => {\n    if (!selectedAccount) return;\n    try {\n      setLoading(true);\n      const response = await api.get(`/facebook/leadforms/${selectedAccount}`);\n      setLeadForms(response.data || []);\n    } catch (error) {\n      console.error('Error loading lead forms:', error);\n      toast.error('Failed to load lead forms');\n      setLeadForms([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const loadAdAccounts = async () => {\n    try {\n      const response = await api.get('/facebook/ad-accounts');\n      setAdAccounts(response.data || []);\n\n      // Auto-select first account if available\n      if (response.data && response.data.length > 0 && !selectedAccount) {\n        setSelectedAccount(response.data[0].id);\n      }\n    } catch (error) {\n      console.error('Error loading ad accounts:', error);\n      setAdAccounts([]);\n    }\n  };\n  const loadFacebookPages = async () => {\n    try {\n      const response = await api.get('/facebook/pages');\n      setFacebookPages(response.data || []);\n    } catch (error) {\n      console.error('Error loading Facebook pages:', error);\n      setFacebookPages([]);\n    }\n  };\n  const loadLeadFormTemplates = async () => {\n    try {\n      const response = await api.get('/facebook/leadform-templates');\n      setLeadFormTemplates(response.data || {});\n    } catch (error) {\n      console.error('Error loading lead form templates:', error);\n      setLeadFormTemplates({\n        questionTypes: [],\n        templates: []\n      });\n    }\n  };\n  const onCreateLeadForm = async data => {\n    try {\n      setLoading(true);\n\n      // Prepare questions array\n      const questions = data.questions.map(q => {\n        var _leadFormTemplates$qu, _leadFormTemplates$qu2;\n        return {\n          type: q.type,\n          key: q.key || q.type.toLowerCase(),\n          label: q.label || ((_leadFormTemplates$qu = leadFormTemplates.questionTypes) === null || _leadFormTemplates$qu === void 0 ? void 0 : (_leadFormTemplates$qu2 = _leadFormTemplates$qu.find(qt => qt.value === q.type)) === null || _leadFormTemplates$qu2 === void 0 ? void 0 : _leadFormTemplates$qu2.label) || q.type\n        };\n      });\n      const leadFormData = {\n        pageId: data.pageId,\n        name: data.name,\n        privacyPolicyUrl: data.privacyPolicyUrl,\n        questions: questions,\n        thankYouPage: data.thankYouPage,\n        contextCard: data.contextCard.title ? data.contextCard : null,\n        followUpActionUrl: data.followUpActionUrl || null,\n        locale: data.locale\n      };\n      await api.post('/facebook/leadforms', leadFormData);\n      toast.success('Lead form created successfully!');\n\n      // Reset form and reload lead forms\n      reset();\n      setShowCreateForm(false);\n      setSelectedTemplate(null);\n      loadLeadForms();\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Error creating lead form:', error);\n      toast.error(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.error) || 'Failed to create lead form');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const applyTemplate = template => {\n    setSelectedTemplate(template);\n    setValue('name', template.name);\n    setValue('questions', template.questions);\n    toast.success(`Applied template: ${template.name}`);\n  };\n  const addQuestion = () => {\n    const currentQuestions = watch('questions') || [];\n    setValue('questions', [...currentQuestions, {\n      type: 'FIRST_NAME',\n      label: 'First Name'\n    }]);\n  };\n  const removeQuestion = index => {\n    const currentQuestions = watch('questions') || [];\n    const newQuestions = currentQuestions.filter((_, i) => i !== index);\n    setValue('questions', newQuestions);\n  };\n  const getStatusIcon = status => {\n    switch (status) {\n      case 'ACTIVE':\n        return /*#__PURE__*/_jsxDEV(CheckCircle, {\n          className: \"h-4 w-4 text-green-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 16\n        }, this);\n      case 'PAUSED':\n        return /*#__PURE__*/_jsxDEV(XCircle, {\n          className: \"h-4 w-4 text-red-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(AlertCircle, {\n          className: \"h-4 w-4 text-yellow-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  const formatDate = dateString => {\n    return new Date(dateString).toLocaleDateString();\n  };\n  if (loading && leadForms.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-gray-500\",\n        children: \"Loading lead forms...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 195,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-bold text-gray-900\",\n          children: \"Facebook Lead Forms\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setShowCreateForm(!showCreateForm),\n          className: \"flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors\",\n          disabled: !selectedAccount,\n          children: [/*#__PURE__*/_jsxDEV(Plus, {\n            className: \"h-4 w-4 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this), showCreateForm ? 'Cancel' : 'Create Lead Form']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"text-sm font-medium text-gray-700\",\n          children: \"Ad Account:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: selectedAccount,\n          onChange: e => setSelectedAccount(e.target.value),\n          className: \"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"\",\n            children: \"Select an ad account\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 13\n          }, this), adAccounts.map(account => /*#__PURE__*/_jsxDEV(\"option\", {\n            value: account.id,\n            children: [account.name, \" (\", account.id, \")\"]\n          }, account.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 15\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center text-sm text-gray-600\",\n          children: [/*#__PURE__*/_jsxDEV(FileText, {\n            className: \"h-4 w-4 mr-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 13\n          }, this), leadForms.length, \" Lead Forms\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 218,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 204,\n      columnNumber: 7\n    }, this), !selectedAccount && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex\",\n        children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n          className: \"h-5 w-5 text-yellow-400 mr-2 mt-0.5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"text-sm font-medium text-yellow-800\",\n            children: \"No Ad Account Selected\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-yellow-700\",\n            children: \"Please select an ad account to view and manage lead forms.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 240,\n      columnNumber: 9\n    }, this), showCreateForm && selectedAccount && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white border border-gray-200 rounded-lg p-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        className: \"text-lg font-semibold text-gray-900 mb-4\",\n        children: \"Create New Lead Form\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 11\n      }, this), leadFormTemplates.templates && leadFormTemplates.templates.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n          className: \"text-sm font-medium text-gray-700 mb-3\",\n          children: \"Quick Start Templates\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3\",\n          children: leadFormTemplates.templates.map((template, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: () => applyTemplate(template),\n            className: `p-3 border rounded-lg text-left hover:bg-blue-50 hover:border-blue-300 transition-colors ${(selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.name) === template.name ? 'border-blue-500 bg-blue-50' : 'border-gray-200'}`,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"font-medium text-sm text-gray-900\",\n              children: template.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs text-gray-500 mt-1\",\n              children: template.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs text-gray-400 mt-1\",\n              children: [template.questions.length, \" questions\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 21\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 260,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit(onCreateLeadForm),\n        className: \"space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Facebook Page *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              ...register('pageId', {\n                required: 'Facebook page is required'\n              }),\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n              children: (facebookPages === null || facebookPages === void 0 ? void 0 : facebookPages.map(page => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: page.id,\n                children: page.name\n              }, page.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 21\n              }, this))) || []\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 17\n            }, this), errors.pageId && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-red-600\",\n              children: errors.pageId.message\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Lead Form Name *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              ...register('name', {\n                required: 'Lead form name is required'\n              }),\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n              placeholder: \"Enter lead form name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 17\n            }, this), errors.name && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-red-600\",\n              children: errors.name.message\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-sm font-medium text-gray-700 mb-2\",\n            children: \"Privacy Policy URL *\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            ...register('privacyPolicyUrl', {\n              required: 'Privacy policy URL is required',\n              pattern: {\n                value: /^https?:\\/\\/.+/,\n                message: 'Please enter a valid URL'\n              }\n            }),\n            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n            placeholder: \"https://example.com/privacy-policy\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 15\n          }, this), errors.privacyPolicyUrl && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-1 text-sm text-red-600\",\n            children: errors.privacyPolicyUrl.message\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 322,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between items-center mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Questions *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 345,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              onClick: addQuestion,\n              className: \"flex items-center px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700\",\n              children: [/*#__PURE__*/_jsxDEV(Plus, {\n                className: \"h-3 w-3 mr-1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 353,\n                columnNumber: 19\n              }, this), \"Add Question\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 348,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3\",\n            children: [(watch('questions') || []).map((question, index) => {\n              var _leadFormTemplates$qu3;\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3 p-3 border border-gray-200 rounded-md\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1\",\n                  children: /*#__PURE__*/_jsxDEV(\"select\", {\n                    ...register(`questions.${index}.type`, {\n                      required: 'Question type is required'\n                    }),\n                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                    children: ((_leadFormTemplates$qu3 = leadFormTemplates.questionTypes) === null || _leadFormTemplates$qu3 === void 0 ? void 0 : _leadFormTemplates$qu3.map(type => /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: type.value,\n                      children: [type.label, \" \", type.required ? '*' : '']\n                    }, type.value, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 367,\n                      columnNumber: 27\n                    }, this))) || []\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 362,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 361,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  onClick: () => removeQuestion(index),\n                  className: \"p-2 text-red-600 hover:text-red-800\",\n                  children: /*#__PURE__*/_jsxDEV(Trash2, {\n                    className: \"h-4 w-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 378,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 373,\n                  columnNumber: 21\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 360,\n                columnNumber: 19\n              }, this);\n            }), (!watch('questions') || watch('questions').length === 0) && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center py-4 text-gray-500 border-2 border-dashed border-gray-300 rounded-md\",\n              children: \"No questions added yet. Click \\\"Add Question\\\" to get started.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 384,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 358,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 343,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Thank You Title\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 394,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              ...register('thankYouPage.title'),\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n              placeholder: \"Thank you!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 397,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 393,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Thank You Message\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 404,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              ...register('thankYouPage.body'),\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n              placeholder: \"We'll be in touch soon.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 407,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 403,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 392,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Context Card Title\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 418,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              ...register('contextCard.title'),\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n              placeholder: \"Optional context title\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 421,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 417,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Follow-up Action URL\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 428,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              ...register('followUpActionUrl'),\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n              placeholder: \"https://example.com/thank-you\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 431,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 427,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 416,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-end space-x-3 pt-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: () => {\n              setShowCreateForm(false);\n              setSelectedTemplate(null);\n              reset();\n            },\n            className: \"px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50\",\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 440,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            disabled: loading,\n            className: \"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50\",\n            children: loading ? 'Creating...' : 'Create Lead Form'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 451,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 439,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 285,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 255,\n      columnNumber: 9\n    }, this), selectedAccount && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white border border-gray-200 rounded-lg overflow-hidden\",\n      children: leadForms.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-8 text-center\",\n        children: [/*#__PURE__*/_jsxDEV(FileText, {\n          className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 468,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-gray-900 mb-2\",\n          children: \"No Lead Forms Found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 469,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-500 mb-4\",\n          children: \"Create your first lead form to start collecting leads from your Facebook ads.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 470,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setShowCreateForm(true),\n          className: \"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700\",\n          children: [/*#__PURE__*/_jsxDEV(Plus, {\n            className: \"h-4 w-4 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 477,\n            columnNumber: 17\n          }, this), \"Create Lead Form\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 473,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 467,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"overflow-x-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"min-w-full divide-y divide-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            className: \"bg-gray-50\",\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Lead Form\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 486,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 489,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Leads\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 492,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Created\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 495,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 498,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 485,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 484,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            className: \"bg-white divide-y divide-gray-200\",\n            children: leadForms.map(form => /*#__PURE__*/_jsxDEV(\"tr\", {\n              className: \"hover:bg-gray-50\",\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm font-medium text-gray-900\",\n                    children: form.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 508,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm text-gray-500\",\n                    children: [\"ID: \", form.id]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 509,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 507,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 506,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [getStatusIcon(form.status), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ml-2 text-sm text-gray-900\",\n                    children: form.status\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 515,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 513,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 512,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center text-sm text-gray-900\",\n                  children: [/*#__PURE__*/_jsxDEV(Users, {\n                    className: \"h-4 w-4 mr-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 520,\n                    columnNumber: 27\n                  }, this), form.leads_count || 0]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 519,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 518,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center text-sm text-gray-900\",\n                  children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                    className: \"h-4 w-4 mr-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 526,\n                    columnNumber: 27\n                  }, this), formatDate(form.created_time)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 525,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 524,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"text-blue-600 hover:text-blue-900\",\n                    children: /*#__PURE__*/_jsxDEV(Eye, {\n                      className: \"h-4 w-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 533,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 532,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"text-gray-600 hover:text-gray-900\",\n                    children: /*#__PURE__*/_jsxDEV(Edit, {\n                      className: \"h-4 w-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 536,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 535,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"text-red-600 hover:text-red-900\",\n                    children: /*#__PURE__*/_jsxDEV(Trash2, {\n                      className: \"h-4 w-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 539,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 538,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 531,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 530,\n                columnNumber: 23\n              }, this)]\n            }, form.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 505,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 503,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 483,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 482,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 465,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 202,\n    columnNumber: 5\n  }, this);\n};\n_s(LeadFormsSection, \"OgUEcsG42agj24htNj0jTwbmoYc=\", false, function () {\n  return [useForm];\n});\n_c = LeadFormsSection;\nexport default LeadFormsSection;\nvar _c;\n$RefreshReg$(_c, \"LeadFormsSection\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useForm", "toast", "FileText", "Plus", "Eye", "Edit", "Trash2", "Users", "Calendar", "CheckCircle", "XCircle", "AlertCircle", "api", "jsxDEV", "_jsxDEV", "LeadFormsSection", "_s", "leadForms", "setLeadForms", "loading", "setLoading", "showCreateForm", "setShowCreateForm", "leadFormTemplates", "setLeadFormTemplates", "selectedTemplate", "setSelectedTemplate", "adAccounts", "setAdAccounts", "facebookPages", "setFacebookPages", "selectedAccount", "setSelectedAccount", "register", "handleSubmit", "watch", "setValue", "reset", "formState", "errors", "defaultValues", "pageId", "name", "privacyPolicyUrl", "questions", "thankYouPage", "title", "body", "contextCard", "content", "followUpActionUrl", "locale", "watchedPageId", "loadAdAccounts", "loadFacebookPages", "loadLeadFormTemplates", "loadLeadForms", "length", "id", "response", "get", "data", "error", "console", "questionTypes", "templates", "onCreateLeadForm", "map", "q", "_leadFormTemplates$qu", "_leadFormTemplates$qu2", "type", "key", "toLowerCase", "label", "find", "qt", "value", "leadFormData", "post", "success", "_error$response", "_error$response$data", "applyTemplate", "template", "addQuestion", "currentQuestions", "removeQuestion", "index", "newQuestions", "filter", "_", "i", "getStatusIcon", "status", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "formatDate", "dateString", "Date", "toLocaleDateString", "children", "onClick", "disabled", "onChange", "e", "target", "account", "description", "onSubmit", "required", "page", "message", "placeholder", "pattern", "question", "_leadFormTemplates$qu3", "form", "leads_count", "created_time", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/FB API SCRAPER/pressure-max-frontend/src/components/LeadFormsSection.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useForm } from 'react-hook-form';\nimport toast from 'react-hot-toast';\nimport { \n  FileText, \n  Plus, \n  Eye, \n  Edit, \n  Trash2, \n  Users, \n  Calendar,\n  CheckCircle,\n  XCircle,\n  AlertCircle\n} from 'lucide-react';\nimport api from '../services/api';\n\nconst LeadFormsSection = () => {\n  const [leadForms, setLeadForms] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [showCreateForm, setShowCreateForm] = useState(false);\n  const [leadFormTemplates, setLeadFormTemplates] = useState({});\n  const [selectedTemplate, setSelectedTemplate] = useState(null);\n  const [adAccounts, setAdAccounts] = useState([]);\n  const [facebookPages, setFacebookPages] = useState([]);\n  const [selectedAccount, setSelectedAccount] = useState('');\n\n  const { register, handleSubmit, watch, setValue, reset, formState: { errors } } = useForm({\n    defaultValues: {\n      pageId: '',\n      name: '',\n      privacyPolicyUrl: '',\n      questions: [],\n      thankYouPage: {\n        title: 'Thank you!',\n        body: 'We\\'ll be in touch soon.'\n      },\n      contextCard: {\n        title: '',\n        content: ''\n      },\n      followUpActionUrl: '',\n      locale: 'en_US'\n    }\n  });\n\n  const watchedPageId = watch('pageId');\n\n  useEffect(() => {\n    loadAdAccounts();\n    loadFacebookPages();\n    loadLeadFormTemplates();\n  }, []);\n\n  useEffect(() => {\n    if (selectedAccount) {\n      loadLeadForms();\n    }\n  }, [selectedAccount]);\n\n  useEffect(() => {\n    // Auto-select first page if available\n    if (facebookPages && facebookPages.length > 0 && !watchedPageId) {\n      setValue('pageId', facebookPages[0].id);\n    }\n  }, [facebookPages, setValue, watchedPageId]);\n\n  const loadLeadForms = async () => {\n    if (!selectedAccount) return;\n    \n    try {\n      setLoading(true);\n      const response = await api.get(`/facebook/leadforms/${selectedAccount}`);\n      setLeadForms(response.data || []);\n    } catch (error) {\n      console.error('Error loading lead forms:', error);\n      toast.error('Failed to load lead forms');\n      setLeadForms([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadAdAccounts = async () => {\n    try {\n      const response = await api.get('/facebook/ad-accounts');\n      setAdAccounts(response.data || []);\n\n      // Auto-select first account if available\n      if (response.data && response.data.length > 0 && !selectedAccount) {\n        setSelectedAccount(response.data[0].id);\n      }\n    } catch (error) {\n      console.error('Error loading ad accounts:', error);\n      setAdAccounts([]);\n    }\n  };\n\n  const loadFacebookPages = async () => {\n    try {\n      const response = await api.get('/facebook/pages');\n      setFacebookPages(response.data || []);\n    } catch (error) {\n      console.error('Error loading Facebook pages:', error);\n      setFacebookPages([]);\n    }\n  };\n\n  const loadLeadFormTemplates = async () => {\n    try {\n      const response = await api.get('/facebook/leadform-templates');\n      setLeadFormTemplates(response.data || {});\n    } catch (error) {\n      console.error('Error loading lead form templates:', error);\n      setLeadFormTemplates({\n        questionTypes: [],\n        templates: []\n      });\n    }\n  };\n\n  const onCreateLeadForm = async (data) => {\n    try {\n      setLoading(true);\n      \n      // Prepare questions array\n      const questions = data.questions.map(q => ({\n        type: q.type,\n        key: q.key || q.type.toLowerCase(),\n        label: q.label || leadFormTemplates.questionTypes?.find(qt => qt.value === q.type)?.label || q.type\n      }));\n\n      const leadFormData = {\n        pageId: data.pageId,\n        name: data.name,\n        privacyPolicyUrl: data.privacyPolicyUrl,\n        questions: questions,\n        thankYouPage: data.thankYouPage,\n        contextCard: data.contextCard.title ? data.contextCard : null,\n        followUpActionUrl: data.followUpActionUrl || null,\n        locale: data.locale\n      };\n\n      await api.post('/facebook/leadforms', leadFormData);\n      toast.success('Lead form created successfully!');\n\n      // Reset form and reload lead forms\n      reset();\n      setShowCreateForm(false);\n      setSelectedTemplate(null);\n      loadLeadForms();\n    } catch (error) {\n      console.error('Error creating lead form:', error);\n      toast.error(error.response?.data?.error || 'Failed to create lead form');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const applyTemplate = (template) => {\n    setSelectedTemplate(template);\n    setValue('name', template.name);\n    setValue('questions', template.questions);\n    toast.success(`Applied template: ${template.name}`);\n  };\n\n  const addQuestion = () => {\n    const currentQuestions = watch('questions') || [];\n    setValue('questions', [...currentQuestions, { type: 'FIRST_NAME', label: 'First Name' }]);\n  };\n\n  const removeQuestion = (index) => {\n    const currentQuestions = watch('questions') || [];\n    const newQuestions = currentQuestions.filter((_, i) => i !== index);\n    setValue('questions', newQuestions);\n  };\n\n  const getStatusIcon = (status) => {\n    switch (status) {\n      case 'ACTIVE':\n        return <CheckCircle className=\"h-4 w-4 text-green-500\" />;\n      case 'PAUSED':\n        return <XCircle className=\"h-4 w-4 text-red-500\" />;\n      default:\n        return <AlertCircle className=\"h-4 w-4 text-yellow-500\" />;\n    }\n  };\n\n  const formatDate = (dateString) => {\n    return new Date(dateString).toLocaleDateString();\n  };\n\n  if (loading && leadForms.length === 0) {\n    return (\n      <div className=\"flex items-center justify-center py-8\">\n        <div className=\"text-gray-500\">Loading lead forms...</div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"space-y-4\">\n        <div className=\"flex justify-between items-center\">\n          <h2 className=\"text-xl font-bold text-gray-900\">Facebook Lead Forms</h2>\n          <button\n            onClick={() => setShowCreateForm(!showCreateForm)}\n            className=\"flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors\"\n            disabled={!selectedAccount}\n          >\n            <Plus className=\"h-4 w-4 mr-2\" />\n            {showCreateForm ? 'Cancel' : 'Create Lead Form'}\n          </button>\n        </div>\n\n        {/* Ad Account Selector */}\n        <div className=\"flex items-center space-x-4\">\n          <label className=\"text-sm font-medium text-gray-700\">Ad Account:</label>\n          <select\n            value={selectedAccount}\n            onChange={(e) => setSelectedAccount(e.target.value)}\n            className=\"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n          >\n            <option value=\"\">Select an ad account</option>\n            {adAccounts.map(account => (\n              <option key={account.id} value={account.id}>\n                {account.name} ({account.id})\n              </option>\n            ))}\n          </select>\n          <div className=\"flex items-center text-sm text-gray-600\">\n            <FileText className=\"h-4 w-4 mr-1\" />\n            {leadForms.length} Lead Forms\n          </div>\n        </div>\n      </div>\n\n      {!selectedAccount && (\n        <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-4\">\n          <div className=\"flex\">\n            <AlertCircle className=\"h-5 w-5 text-yellow-400 mr-2 mt-0.5\" />\n            <div>\n              <h4 className=\"text-sm font-medium text-yellow-800\">No Ad Account Selected</h4>\n              <p className=\"text-sm text-yellow-700\">\n                Please select an ad account to view and manage lead forms.\n              </p>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Create Form */}\n      {showCreateForm && selectedAccount && (\n        <div className=\"bg-white border border-gray-200 rounded-lg p-6\">\n          <h4 className=\"text-lg font-semibold text-gray-900 mb-4\">Create New Lead Form</h4>\n          \n          {/* Templates */}\n          {leadFormTemplates.templates && leadFormTemplates.templates.length > 0 && (\n            <div className=\"mb-6\">\n              <h5 className=\"text-sm font-medium text-gray-700 mb-3\">Quick Start Templates</h5>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3\">\n                {leadFormTemplates.templates.map((template, index) => (\n                  <button\n                    key={index}\n                    type=\"button\"\n                    onClick={() => applyTemplate(template)}\n                    className={`p-3 border rounded-lg text-left hover:bg-blue-50 hover:border-blue-300 transition-colors ${\n                      selectedTemplate?.name === template.name \n                        ? 'border-blue-500 bg-blue-50' \n                        : 'border-gray-200'\n                    }`}\n                  >\n                    <div className=\"font-medium text-sm text-gray-900\">{template.name}</div>\n                    <div className=\"text-xs text-gray-500 mt-1\">{template.description}</div>\n                    <div className=\"text-xs text-gray-400 mt-1\">\n                      {template.questions.length} questions\n                    </div>\n                  </button>\n                ))}\n              </div>\n            </div>\n          )}\n\n          <form onSubmit={handleSubmit(onCreateLeadForm)} className=\"space-y-4\">\n            {/* Basic Info */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Facebook Page *\n                </label>\n                <select\n                  {...register('pageId', { required: 'Facebook page is required' })}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                >\n                  {facebookPages?.map(page => (\n                    <option key={page.id} value={page.id}>\n                      {page.name}\n                    </option>\n                  )) || []}\n                </select>\n                {errors.pageId && (\n                  <p className=\"mt-1 text-sm text-red-600\">{errors.pageId.message}</p>\n                )}\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Lead Form Name *\n                </label>\n                <input\n                  {...register('name', { required: 'Lead form name is required' })}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  placeholder=\"Enter lead form name\"\n                />\n                {errors.name && (\n                  <p className=\"mt-1 text-sm text-red-600\">{errors.name.message}</p>\n                )}\n              </div>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Privacy Policy URL *\n              </label>\n              <input\n                {...register('privacyPolicyUrl', { \n                  required: 'Privacy policy URL is required',\n                  pattern: {\n                    value: /^https?:\\/\\/.+/,\n                    message: 'Please enter a valid URL'\n                  }\n                })}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                placeholder=\"https://example.com/privacy-policy\"\n              />\n              {errors.privacyPolicyUrl && (\n                <p className=\"mt-1 text-sm text-red-600\">{errors.privacyPolicyUrl.message}</p>\n              )}\n            </div>\n\n            {/* Questions Section */}\n            <div>\n              <div className=\"flex justify-between items-center mb-3\">\n                <label className=\"block text-sm font-medium text-gray-700\">\n                  Questions *\n                </label>\n                <button\n                  type=\"button\"\n                  onClick={addQuestion}\n                  className=\"flex items-center px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700\"\n                >\n                  <Plus className=\"h-3 w-3 mr-1\" />\n                  Add Question\n                </button>\n              </div>\n\n              <div className=\"space-y-3\">\n                {(watch('questions') || []).map((question, index) => (\n                  <div key={index} className=\"flex items-center space-x-3 p-3 border border-gray-200 rounded-md\">\n                    <div className=\"flex-1\">\n                      <select\n                        {...register(`questions.${index}.type`, { required: 'Question type is required' })}\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                      >\n                        {leadFormTemplates.questionTypes?.map(type => (\n                          <option key={type.value} value={type.value}>\n                            {type.label} {type.required ? '*' : ''}\n                          </option>\n                        )) || []}\n                      </select>\n                    </div>\n                    <button\n                      type=\"button\"\n                      onClick={() => removeQuestion(index)}\n                      className=\"p-2 text-red-600 hover:text-red-800\"\n                    >\n                      <Trash2 className=\"h-4 w-4\" />\n                    </button>\n                  </div>\n                ))}\n\n                {(!watch('questions') || watch('questions').length === 0) && (\n                  <div className=\"text-center py-4 text-gray-500 border-2 border-dashed border-gray-300 rounded-md\">\n                    No questions added yet. Click \"Add Question\" to get started.\n                  </div>\n                )}\n              </div>\n            </div>\n\n            {/* Thank You Page */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Thank You Title\n                </label>\n                <input\n                  {...register('thankYouPage.title')}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  placeholder=\"Thank you!\"\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Thank You Message\n                </label>\n                <input\n                  {...register('thankYouPage.body')}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  placeholder=\"We'll be in touch soon.\"\n                />\n              </div>\n            </div>\n\n            {/* Optional Fields */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Context Card Title\n                </label>\n                <input\n                  {...register('contextCard.title')}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  placeholder=\"Optional context title\"\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Follow-up Action URL\n                </label>\n                <input\n                  {...register('followUpActionUrl')}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  placeholder=\"https://example.com/thank-you\"\n                />\n              </div>\n            </div>\n\n            <div className=\"flex justify-end space-x-3 pt-4\">\n              <button\n                type=\"button\"\n                onClick={() => {\n                  setShowCreateForm(false);\n                  setSelectedTemplate(null);\n                  reset();\n                }}\n                className=\"px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50\"\n              >\n                Cancel\n              </button>\n              <button\n                type=\"submit\"\n                disabled={loading}\n                className=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50\"\n              >\n                {loading ? 'Creating...' : 'Create Lead Form'}\n              </button>\n            </div>\n          </form>\n        </div>\n      )}\n\n      {/* Lead Forms List */}\n      {selectedAccount && (\n        <div className=\"bg-white border border-gray-200 rounded-lg overflow-hidden\">\n          {leadForms.length === 0 ? (\n            <div className=\"p-8 text-center\">\n              <FileText className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n              <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No Lead Forms Found</h3>\n              <p className=\"text-gray-500 mb-4\">\n                Create your first lead form to start collecting leads from your Facebook ads.\n              </p>\n              <button\n                onClick={() => setShowCreateForm(true)}\n                className=\"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700\"\n              >\n                <Plus className=\"h-4 w-4 mr-2\" />\n                Create Lead Form\n              </button>\n            </div>\n          ) : (\n            <div className=\"overflow-x-auto\">\n              <table className=\"min-w-full divide-y divide-gray-200\">\n                <thead className=\"bg-gray-50\">\n                  <tr>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Lead Form\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Status\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Leads\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Created\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Actions\n                    </th>\n                  </tr>\n                </thead>\n                <tbody className=\"bg-white divide-y divide-gray-200\">\n                  {leadForms.map((form) => (\n                    <tr key={form.id} className=\"hover:bg-gray-50\">\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <div>\n                          <div className=\"text-sm font-medium text-gray-900\">{form.name}</div>\n                          <div className=\"text-sm text-gray-500\">ID: {form.id}</div>\n                        </div>\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <div className=\"flex items-center\">\n                          {getStatusIcon(form.status)}\n                          <span className=\"ml-2 text-sm text-gray-900\">{form.status}</span>\n                        </div>\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <div className=\"flex items-center text-sm text-gray-900\">\n                          <Users className=\"h-4 w-4 mr-1\" />\n                          {form.leads_count || 0}\n                        </div>\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <div className=\"flex items-center text-sm text-gray-900\">\n                          <Calendar className=\"h-4 w-4 mr-1\" />\n                          {formatDate(form.created_time)}\n                        </div>\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                        <div className=\"flex space-x-2\">\n                          <button className=\"text-blue-600 hover:text-blue-900\">\n                            <Eye className=\"h-4 w-4\" />\n                          </button>\n                          <button className=\"text-gray-600 hover:text-gray-900\">\n                            <Edit className=\"h-4 w-4\" />\n                          </button>\n                          <button className=\"text-red-600 hover:text-red-900\">\n                            <Trash2 className=\"h-4 w-4\" />\n                          </button>\n                        </div>\n                      </td>\n                    </tr>\n                  ))}\n                </tbody>\n              </table>\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default LeadFormsSection;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,iBAAiB;AACzC,OAAOC,KAAK,MAAM,iBAAiB;AACnC,SACEC,QAAQ,EACRC,IAAI,EACJC,GAAG,EACHC,IAAI,EACJC,MAAM,EACNC,KAAK,EACLC,QAAQ,EACRC,WAAW,EACXC,OAAO,EACPC,WAAW,QACN,cAAc;AACrB,OAAOC,GAAG,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACuB,cAAc,EAAEC,iBAAiB,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACyB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG1B,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC9D,MAAM,CAAC2B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAAC6B,UAAU,EAAEC,aAAa,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC+B,aAAa,EAAEC,gBAAgB,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACiC,eAAe,EAAEC,kBAAkB,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAE1D,MAAM;IAAEmC,QAAQ;IAAEC,YAAY;IAAEC,KAAK;IAAEC,QAAQ;IAAEC,KAAK;IAAEC,SAAS,EAAE;MAAEC;IAAO;EAAE,CAAC,GAAGvC,OAAO,CAAC;IACxFwC,aAAa,EAAE;MACbC,MAAM,EAAE,EAAE;MACVC,IAAI,EAAE,EAAE;MACRC,gBAAgB,EAAE,EAAE;MACpBC,SAAS,EAAE,EAAE;MACbC,YAAY,EAAE;QACZC,KAAK,EAAE,YAAY;QACnBC,IAAI,EAAE;MACR,CAAC;MACDC,WAAW,EAAE;QACXF,KAAK,EAAE,EAAE;QACTG,OAAO,EAAE;MACX,CAAC;MACDC,iBAAiB,EAAE,EAAE;MACrBC,MAAM,EAAE;IACV;EACF,CAAC,CAAC;EAEF,MAAMC,aAAa,GAAGjB,KAAK,CAAC,QAAQ,CAAC;EAErCpC,SAAS,CAAC,MAAM;IACdsD,cAAc,CAAC,CAAC;IAChBC,iBAAiB,CAAC,CAAC;IACnBC,qBAAqB,CAAC,CAAC;EACzB,CAAC,EAAE,EAAE,CAAC;EAENxD,SAAS,CAAC,MAAM;IACd,IAAIgC,eAAe,EAAE;MACnByB,aAAa,CAAC,CAAC;IACjB;EACF,CAAC,EAAE,CAACzB,eAAe,CAAC,CAAC;EAErBhC,SAAS,CAAC,MAAM;IACd;IACA,IAAI8B,aAAa,IAAIA,aAAa,CAAC4B,MAAM,GAAG,CAAC,IAAI,CAACL,aAAa,EAAE;MAC/DhB,QAAQ,CAAC,QAAQ,EAAEP,aAAa,CAAC,CAAC,CAAC,CAAC6B,EAAE,CAAC;IACzC;EACF,CAAC,EAAE,CAAC7B,aAAa,EAAEO,QAAQ,EAAEgB,aAAa,CAAC,CAAC;EAE5C,MAAMI,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI,CAACzB,eAAe,EAAE;IAEtB,IAAI;MACFX,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMuC,QAAQ,GAAG,MAAM/C,GAAG,CAACgD,GAAG,CAAC,uBAAuB7B,eAAe,EAAE,CAAC;MACxEb,YAAY,CAACyC,QAAQ,CAACE,IAAI,IAAI,EAAE,CAAC;IACnC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD7D,KAAK,CAAC6D,KAAK,CAAC,2BAA2B,CAAC;MACxC5C,YAAY,CAAC,EAAE,CAAC;IAClB,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMiC,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF,MAAMM,QAAQ,GAAG,MAAM/C,GAAG,CAACgD,GAAG,CAAC,uBAAuB,CAAC;MACvDhC,aAAa,CAAC+B,QAAQ,CAACE,IAAI,IAAI,EAAE,CAAC;;MAElC;MACA,IAAIF,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACJ,MAAM,GAAG,CAAC,IAAI,CAAC1B,eAAe,EAAE;QACjEC,kBAAkB,CAAC2B,QAAQ,CAACE,IAAI,CAAC,CAAC,CAAC,CAACH,EAAE,CAAC;MACzC;IACF,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClDlC,aAAa,CAAC,EAAE,CAAC;IACnB;EACF,CAAC;EAED,MAAM0B,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACF,MAAMK,QAAQ,GAAG,MAAM/C,GAAG,CAACgD,GAAG,CAAC,iBAAiB,CAAC;MACjD9B,gBAAgB,CAAC6B,QAAQ,CAACE,IAAI,IAAI,EAAE,CAAC;IACvC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrDhC,gBAAgB,CAAC,EAAE,CAAC;IACtB;EACF,CAAC;EAED,MAAMyB,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC,IAAI;MACF,MAAMI,QAAQ,GAAG,MAAM/C,GAAG,CAACgD,GAAG,CAAC,8BAA8B,CAAC;MAC9DpC,oBAAoB,CAACmC,QAAQ,CAACE,IAAI,IAAI,CAAC,CAAC,CAAC;IAC3C,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1DtC,oBAAoB,CAAC;QACnBwC,aAAa,EAAE,EAAE;QACjBC,SAAS,EAAE;MACb,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMC,gBAAgB,GAAG,MAAOL,IAAI,IAAK;IACvC,IAAI;MACFzC,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAMwB,SAAS,GAAGiB,IAAI,CAACjB,SAAS,CAACuB,GAAG,CAACC,CAAC;QAAA,IAAAC,qBAAA,EAAAC,sBAAA;QAAA,OAAK;UACzCC,IAAI,EAAEH,CAAC,CAACG,IAAI;UACZC,GAAG,EAAEJ,CAAC,CAACI,GAAG,IAAIJ,CAAC,CAACG,IAAI,CAACE,WAAW,CAAC,CAAC;UAClCC,KAAK,EAAEN,CAAC,CAACM,KAAK,MAAAL,qBAAA,GAAI9C,iBAAiB,CAACyC,aAAa,cAAAK,qBAAA,wBAAAC,sBAAA,GAA/BD,qBAAA,CAAiCM,IAAI,CAACC,EAAE,IAAIA,EAAE,CAACC,KAAK,KAAKT,CAAC,CAACG,IAAI,CAAC,cAAAD,sBAAA,uBAAhEA,sBAAA,CAAkEI,KAAK,KAAIN,CAAC,CAACG;QACjG,CAAC;MAAA,CAAC,CAAC;MAEH,MAAMO,YAAY,GAAG;QACnBrC,MAAM,EAAEoB,IAAI,CAACpB,MAAM;QACnBC,IAAI,EAAEmB,IAAI,CAACnB,IAAI;QACfC,gBAAgB,EAAEkB,IAAI,CAAClB,gBAAgB;QACvCC,SAAS,EAAEA,SAAS;QACpBC,YAAY,EAAEgB,IAAI,CAAChB,YAAY;QAC/BG,WAAW,EAAEa,IAAI,CAACb,WAAW,CAACF,KAAK,GAAGe,IAAI,CAACb,WAAW,GAAG,IAAI;QAC7DE,iBAAiB,EAAEW,IAAI,CAACX,iBAAiB,IAAI,IAAI;QACjDC,MAAM,EAAEU,IAAI,CAACV;MACf,CAAC;MAED,MAAMvC,GAAG,CAACmE,IAAI,CAAC,qBAAqB,EAAED,YAAY,CAAC;MACnD7E,KAAK,CAAC+E,OAAO,CAAC,iCAAiC,CAAC;;MAEhD;MACA3C,KAAK,CAAC,CAAC;MACPf,iBAAiB,CAAC,KAAK,CAAC;MACxBI,mBAAmB,CAAC,IAAI,CAAC;MACzB8B,aAAa,CAAC,CAAC;IACjB,CAAC,CAAC,OAAOM,KAAK,EAAE;MAAA,IAAAmB,eAAA,EAAAC,oBAAA;MACdnB,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD7D,KAAK,CAAC6D,KAAK,CAAC,EAAAmB,eAAA,GAAAnB,KAAK,CAACH,QAAQ,cAAAsB,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBpB,IAAI,cAAAqB,oBAAA,uBAApBA,oBAAA,CAAsBpB,KAAK,KAAI,4BAA4B,CAAC;IAC1E,CAAC,SAAS;MACR1C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM+D,aAAa,GAAIC,QAAQ,IAAK;IAClC1D,mBAAmB,CAAC0D,QAAQ,CAAC;IAC7BhD,QAAQ,CAAC,MAAM,EAAEgD,QAAQ,CAAC1C,IAAI,CAAC;IAC/BN,QAAQ,CAAC,WAAW,EAAEgD,QAAQ,CAACxC,SAAS,CAAC;IACzC3C,KAAK,CAAC+E,OAAO,CAAC,qBAAqBI,QAAQ,CAAC1C,IAAI,EAAE,CAAC;EACrD,CAAC;EAED,MAAM2C,WAAW,GAAGA,CAAA,KAAM;IACxB,MAAMC,gBAAgB,GAAGnD,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE;IACjDC,QAAQ,CAAC,WAAW,EAAE,CAAC,GAAGkD,gBAAgB,EAAE;MAAEf,IAAI,EAAE,YAAY;MAAEG,KAAK,EAAE;IAAa,CAAC,CAAC,CAAC;EAC3F,CAAC;EAED,MAAMa,cAAc,GAAIC,KAAK,IAAK;IAChC,MAAMF,gBAAgB,GAAGnD,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE;IACjD,MAAMsD,YAAY,GAAGH,gBAAgB,CAACI,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAKJ,KAAK,CAAC;IACnEpD,QAAQ,CAAC,WAAW,EAAEqD,YAAY,CAAC;EACrC,CAAC;EAED,MAAMI,aAAa,GAAIC,MAAM,IAAK;IAChC,QAAQA,MAAM;MACZ,KAAK,QAAQ;QACX,oBAAOhF,OAAA,CAACL,WAAW;UAACsF,SAAS,EAAC;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC3D,KAAK,QAAQ;QACX,oBAAOrF,OAAA,CAACJ,OAAO;UAACqF,SAAS,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACrD;QACE,oBAAOrF,OAAA,CAACH,WAAW;UAACoF,SAAS,EAAC;QAAyB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IAC9D;EACF,CAAC;EAED,MAAMC,UAAU,GAAIC,UAAU,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,CAAC;EAClD,CAAC;EAED,IAAIpF,OAAO,IAAIF,SAAS,CAACwC,MAAM,KAAK,CAAC,EAAE;IACrC,oBACE3C,OAAA;MAAKiF,SAAS,EAAC,uCAAuC;MAAAS,QAAA,eACpD1F,OAAA;QAAKiF,SAAS,EAAC,eAAe;QAAAS,QAAA,EAAC;MAAqB;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvD,CAAC;EAEV;EAEA,oBACErF,OAAA;IAAKiF,SAAS,EAAC,WAAW;IAAAS,QAAA,gBAExB1F,OAAA;MAAKiF,SAAS,EAAC,WAAW;MAAAS,QAAA,gBACxB1F,OAAA;QAAKiF,SAAS,EAAC,mCAAmC;QAAAS,QAAA,gBAChD1F,OAAA;UAAIiF,SAAS,EAAC,iCAAiC;UAAAS,QAAA,EAAC;QAAmB;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxErF,OAAA;UACE2F,OAAO,EAAEA,CAAA,KAAMnF,iBAAiB,CAAC,CAACD,cAAc,CAAE;UAClD0E,SAAS,EAAC,mGAAmG;UAC7GW,QAAQ,EAAE,CAAC3E,eAAgB;UAAAyE,QAAA,gBAE3B1F,OAAA,CAACX,IAAI;YAAC4F,SAAS,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAChC9E,cAAc,GAAG,QAAQ,GAAG,kBAAkB;QAAA;UAAA2E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGNrF,OAAA;QAAKiF,SAAS,EAAC,6BAA6B;QAAAS,QAAA,gBAC1C1F,OAAA;UAAOiF,SAAS,EAAC,mCAAmC;UAAAS,QAAA,EAAC;QAAW;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACxErF,OAAA;UACE+D,KAAK,EAAE9C,eAAgB;UACvB4E,QAAQ,EAAGC,CAAC,IAAK5E,kBAAkB,CAAC4E,CAAC,CAACC,MAAM,CAAChC,KAAK,CAAE;UACpDkB,SAAS,EAAC,iGAAiG;UAAAS,QAAA,gBAE3G1F,OAAA;YAAQ+D,KAAK,EAAC,EAAE;YAAA2B,QAAA,EAAC;UAAoB;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EAC7CxE,UAAU,CAACwC,GAAG,CAAC2C,OAAO,iBACrBhG,OAAA;YAAyB+D,KAAK,EAAEiC,OAAO,CAACpD,EAAG;YAAA8C,QAAA,GACxCM,OAAO,CAACpE,IAAI,EAAC,IAAE,EAACoE,OAAO,CAACpD,EAAE,EAAC,GAC9B;UAAA,GAFaoD,OAAO,CAACpD,EAAE;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEf,CACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eACTrF,OAAA;UAAKiF,SAAS,EAAC,yCAAyC;UAAAS,QAAA,gBACtD1F,OAAA,CAACZ,QAAQ;YAAC6F,SAAS,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACpClF,SAAS,CAACwC,MAAM,EAAC,aACpB;QAAA;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAEL,CAACpE,eAAe,iBACfjB,OAAA;MAAKiF,SAAS,EAAC,sDAAsD;MAAAS,QAAA,eACnE1F,OAAA;QAAKiF,SAAS,EAAC,MAAM;QAAAS,QAAA,gBACnB1F,OAAA,CAACH,WAAW;UAACoF,SAAS,EAAC;QAAqC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC/DrF,OAAA;UAAA0F,QAAA,gBACE1F,OAAA;YAAIiF,SAAS,EAAC,qCAAqC;YAAAS,QAAA,EAAC;UAAsB;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/ErF,OAAA;YAAGiF,SAAS,EAAC,yBAAyB;YAAAS,QAAA,EAAC;UAEvC;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGA9E,cAAc,IAAIU,eAAe,iBAChCjB,OAAA;MAAKiF,SAAS,EAAC,gDAAgD;MAAAS,QAAA,gBAC7D1F,OAAA;QAAIiF,SAAS,EAAC,0CAA0C;QAAAS,QAAA,EAAC;MAAoB;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAGjF5E,iBAAiB,CAAC0C,SAAS,IAAI1C,iBAAiB,CAAC0C,SAAS,CAACR,MAAM,GAAG,CAAC,iBACpE3C,OAAA;QAAKiF,SAAS,EAAC,MAAM;QAAAS,QAAA,gBACnB1F,OAAA;UAAIiF,SAAS,EAAC,wCAAwC;UAAAS,QAAA,EAAC;QAAqB;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjFrF,OAAA;UAAKiF,SAAS,EAAC,sDAAsD;UAAAS,QAAA,EAClEjF,iBAAiB,CAAC0C,SAAS,CAACE,GAAG,CAAC,CAACiB,QAAQ,EAAEI,KAAK,kBAC/C1E,OAAA;YAEEyD,IAAI,EAAC,QAAQ;YACbkC,OAAO,EAAEA,CAAA,KAAMtB,aAAa,CAACC,QAAQ,CAAE;YACvCW,SAAS,EAAE,4FACT,CAAAtE,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEiB,IAAI,MAAK0C,QAAQ,CAAC1C,IAAI,GACpC,4BAA4B,GAC5B,iBAAiB,EACpB;YAAA8D,QAAA,gBAEH1F,OAAA;cAAKiF,SAAS,EAAC,mCAAmC;cAAAS,QAAA,EAAEpB,QAAQ,CAAC1C;YAAI;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxErF,OAAA;cAAKiF,SAAS,EAAC,4BAA4B;cAAAS,QAAA,EAAEpB,QAAQ,CAAC2B;YAAW;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxErF,OAAA;cAAKiF,SAAS,EAAC,4BAA4B;cAAAS,QAAA,GACxCpB,QAAQ,CAACxC,SAAS,CAACa,MAAM,EAAC,YAC7B;YAAA;cAAAuC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA,GAbDX,KAAK;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAcJ,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAEDrF,OAAA;QAAMkG,QAAQ,EAAE9E,YAAY,CAACgC,gBAAgB,CAAE;QAAC6B,SAAS,EAAC,WAAW;QAAAS,QAAA,gBAEnE1F,OAAA;UAAKiF,SAAS,EAAC,uCAAuC;UAAAS,QAAA,gBACpD1F,OAAA;YAAA0F,QAAA,gBACE1F,OAAA;cAAOiF,SAAS,EAAC,8CAA8C;cAAAS,QAAA,EAAC;YAEhE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRrF,OAAA;cAAA,GACMmB,QAAQ,CAAC,QAAQ,EAAE;gBAAEgF,QAAQ,EAAE;cAA4B,CAAC,CAAC;cACjElB,SAAS,EAAC,wGAAwG;cAAAS,QAAA,EAEjH,CAAA3E,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEsC,GAAG,CAAC+C,IAAI,iBACtBpG,OAAA;gBAAsB+D,KAAK,EAAEqC,IAAI,CAACxD,EAAG;gBAAA8C,QAAA,EAClCU,IAAI,CAACxE;cAAI,GADCwE,IAAI,CAACxD,EAAE;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEZ,CACT,CAAC,KAAI;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,EACR5D,MAAM,CAACE,MAAM,iBACZ3B,OAAA;cAAGiF,SAAS,EAAC,2BAA2B;cAAAS,QAAA,EAAEjE,MAAM,CAACE,MAAM,CAAC0E;YAAO;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CACpE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENrF,OAAA;YAAA0F,QAAA,gBACE1F,OAAA;cAAOiF,SAAS,EAAC,8CAA8C;cAAAS,QAAA,EAAC;YAEhE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRrF,OAAA;cAAA,GACMmB,QAAQ,CAAC,MAAM,EAAE;gBAAEgF,QAAQ,EAAE;cAA6B,CAAC,CAAC;cAChElB,SAAS,EAAC,wGAAwG;cAClHqB,WAAW,EAAC;YAAsB;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,EACD5D,MAAM,CAACG,IAAI,iBACV5B,OAAA;cAAGiF,SAAS,EAAC,2BAA2B;cAAAS,QAAA,EAAEjE,MAAM,CAACG,IAAI,CAACyE;YAAO;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAClE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENrF,OAAA;UAAA0F,QAAA,gBACE1F,OAAA;YAAOiF,SAAS,EAAC,8CAA8C;YAAAS,QAAA,EAAC;UAEhE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRrF,OAAA;YAAA,GACMmB,QAAQ,CAAC,kBAAkB,EAAE;cAC/BgF,QAAQ,EAAE,gCAAgC;cAC1CI,OAAO,EAAE;gBACPxC,KAAK,EAAE,gBAAgB;gBACvBsC,OAAO,EAAE;cACX;YACF,CAAC,CAAC;YACFpB,SAAS,EAAC,wGAAwG;YAClHqB,WAAW,EAAC;UAAoC;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC,EACD5D,MAAM,CAACI,gBAAgB,iBACtB7B,OAAA;YAAGiF,SAAS,EAAC,2BAA2B;YAAAS,QAAA,EAAEjE,MAAM,CAACI,gBAAgB,CAACwE;UAAO;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAC9E;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNrF,OAAA;UAAA0F,QAAA,gBACE1F,OAAA;YAAKiF,SAAS,EAAC,wCAAwC;YAAAS,QAAA,gBACrD1F,OAAA;cAAOiF,SAAS,EAAC,yCAAyC;cAAAS,QAAA,EAAC;YAE3D;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRrF,OAAA;cACEyD,IAAI,EAAC,QAAQ;cACbkC,OAAO,EAAEpB,WAAY;cACrBU,SAAS,EAAC,yFAAyF;cAAAS,QAAA,gBAEnG1F,OAAA,CAACX,IAAI;gBAAC4F,SAAS,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAEnC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENrF,OAAA;YAAKiF,SAAS,EAAC,WAAW;YAAAS,QAAA,GACvB,CAACrE,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,EAAEgC,GAAG,CAAC,CAACmD,QAAQ,EAAE9B,KAAK;cAAA,IAAA+B,sBAAA;cAAA,oBAC9CzG,OAAA;gBAAiBiF,SAAS,EAAC,mEAAmE;gBAAAS,QAAA,gBAC5F1F,OAAA;kBAAKiF,SAAS,EAAC,QAAQ;kBAAAS,QAAA,eACrB1F,OAAA;oBAAA,GACMmB,QAAQ,CAAC,aAAauD,KAAK,OAAO,EAAE;sBAAEyB,QAAQ,EAAE;oBAA4B,CAAC,CAAC;oBAClFlB,SAAS,EAAC,wGAAwG;oBAAAS,QAAA,EAEjH,EAAAe,sBAAA,GAAAhG,iBAAiB,CAACyC,aAAa,cAAAuD,sBAAA,uBAA/BA,sBAAA,CAAiCpD,GAAG,CAACI,IAAI,iBACxCzD,OAAA;sBAAyB+D,KAAK,EAAEN,IAAI,CAACM,KAAM;sBAAA2B,QAAA,GACxCjC,IAAI,CAACG,KAAK,EAAC,GAAC,EAACH,IAAI,CAAC0C,QAAQ,GAAG,GAAG,GAAG,EAAE;oBAAA,GAD3B1C,IAAI,CAACM,KAAK;sBAAAmB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEf,CACT,CAAC,KAAI;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACNrF,OAAA;kBACEyD,IAAI,EAAC,QAAQ;kBACbkC,OAAO,EAAEA,CAAA,KAAMlB,cAAc,CAACC,KAAK,CAAE;kBACrCO,SAAS,EAAC,qCAAqC;kBAAAS,QAAA,eAE/C1F,OAAA,CAACR,MAAM;oBAACyF,SAAS,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA,GAnBDX,KAAK;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAoBV,CAAC;YAAA,CACP,CAAC,EAED,CAAC,CAAChE,KAAK,CAAC,WAAW,CAAC,IAAIA,KAAK,CAAC,WAAW,CAAC,CAACsB,MAAM,KAAK,CAAC,kBACtD3C,OAAA;cAAKiF,SAAS,EAAC,kFAAkF;cAAAS,QAAA,EAAC;YAElG;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNrF,OAAA;UAAKiF,SAAS,EAAC,uCAAuC;UAAAS,QAAA,gBACpD1F,OAAA;YAAA0F,QAAA,gBACE1F,OAAA;cAAOiF,SAAS,EAAC,8CAA8C;cAAAS,QAAA,EAAC;YAEhE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRrF,OAAA;cAAA,GACMmB,QAAQ,CAAC,oBAAoB,CAAC;cAClC8D,SAAS,EAAC,wGAAwG;cAClHqB,WAAW,EAAC;YAAY;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNrF,OAAA;YAAA0F,QAAA,gBACE1F,OAAA;cAAOiF,SAAS,EAAC,8CAA8C;cAAAS,QAAA,EAAC;YAEhE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRrF,OAAA;cAAA,GACMmB,QAAQ,CAAC,mBAAmB,CAAC;cACjC8D,SAAS,EAAC,wGAAwG;cAClHqB,WAAW,EAAC;YAAyB;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNrF,OAAA;UAAKiF,SAAS,EAAC,uCAAuC;UAAAS,QAAA,gBACpD1F,OAAA;YAAA0F,QAAA,gBACE1F,OAAA;cAAOiF,SAAS,EAAC,8CAA8C;cAAAS,QAAA,EAAC;YAEhE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRrF,OAAA;cAAA,GACMmB,QAAQ,CAAC,mBAAmB,CAAC;cACjC8D,SAAS,EAAC,wGAAwG;cAClHqB,WAAW,EAAC;YAAwB;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNrF,OAAA;YAAA0F,QAAA,gBACE1F,OAAA;cAAOiF,SAAS,EAAC,8CAA8C;cAAAS,QAAA,EAAC;YAEhE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRrF,OAAA;cAAA,GACMmB,QAAQ,CAAC,mBAAmB,CAAC;cACjC8D,SAAS,EAAC,wGAAwG;cAClHqB,WAAW,EAAC;YAA+B;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENrF,OAAA;UAAKiF,SAAS,EAAC,iCAAiC;UAAAS,QAAA,gBAC9C1F,OAAA;YACEyD,IAAI,EAAC,QAAQ;YACbkC,OAAO,EAAEA,CAAA,KAAM;cACbnF,iBAAiB,CAAC,KAAK,CAAC;cACxBI,mBAAmB,CAAC,IAAI,CAAC;cACzBW,KAAK,CAAC,CAAC;YACT,CAAE;YACF0D,SAAS,EAAC,qFAAqF;YAAAS,QAAA,EAChG;UAED;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTrF,OAAA;YACEyD,IAAI,EAAC,QAAQ;YACbmC,QAAQ,EAAEvF,OAAQ;YAClB4E,SAAS,EAAC,mFAAmF;YAAAS,QAAA,EAE5FrF,OAAO,GAAG,aAAa,GAAG;UAAkB;YAAA6E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CACN,EAGApE,eAAe,iBACdjB,OAAA;MAAKiF,SAAS,EAAC,4DAA4D;MAAAS,QAAA,EACxEvF,SAAS,CAACwC,MAAM,KAAK,CAAC,gBACrB3C,OAAA;QAAKiF,SAAS,EAAC,iBAAiB;QAAAS,QAAA,gBAC9B1F,OAAA,CAACZ,QAAQ;UAAC6F,SAAS,EAAC;QAAsC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7DrF,OAAA;UAAIiF,SAAS,EAAC,wCAAwC;UAAAS,QAAA,EAAC;QAAmB;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/ErF,OAAA;UAAGiF,SAAS,EAAC,oBAAoB;UAAAS,QAAA,EAAC;QAElC;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJrF,OAAA;UACE2F,OAAO,EAAEA,CAAA,KAAMnF,iBAAiB,CAAC,IAAI,CAAE;UACvCyE,SAAS,EAAC,wFAAwF;UAAAS,QAAA,gBAElG1F,OAAA,CAACX,IAAI;YAAC4F,SAAS,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,oBAEnC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,gBAENrF,OAAA;QAAKiF,SAAS,EAAC,iBAAiB;QAAAS,QAAA,eAC9B1F,OAAA;UAAOiF,SAAS,EAAC,qCAAqC;UAAAS,QAAA,gBACpD1F,OAAA;YAAOiF,SAAS,EAAC,YAAY;YAAAS,QAAA,eAC3B1F,OAAA;cAAA0F,QAAA,gBACE1F,OAAA;gBAAIiF,SAAS,EAAC,gFAAgF;gBAAAS,QAAA,EAAC;cAE/F;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLrF,OAAA;gBAAIiF,SAAS,EAAC,gFAAgF;gBAAAS,QAAA,EAAC;cAE/F;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLrF,OAAA;gBAAIiF,SAAS,EAAC,gFAAgF;gBAAAS,QAAA,EAAC;cAE/F;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLrF,OAAA;gBAAIiF,SAAS,EAAC,gFAAgF;gBAAAS,QAAA,EAAC;cAE/F;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLrF,OAAA;gBAAIiF,SAAS,EAAC,gFAAgF;gBAAAS,QAAA,EAAC;cAE/F;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRrF,OAAA;YAAOiF,SAAS,EAAC,mCAAmC;YAAAS,QAAA,EACjDvF,SAAS,CAACkD,GAAG,CAAEqD,IAAI,iBAClB1G,OAAA;cAAkBiF,SAAS,EAAC,kBAAkB;cAAAS,QAAA,gBAC5C1F,OAAA;gBAAIiF,SAAS,EAAC,6BAA6B;gBAAAS,QAAA,eACzC1F,OAAA;kBAAA0F,QAAA,gBACE1F,OAAA;oBAAKiF,SAAS,EAAC,mCAAmC;oBAAAS,QAAA,EAAEgB,IAAI,CAAC9E;kBAAI;oBAAAsD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACpErF,OAAA;oBAAKiF,SAAS,EAAC,uBAAuB;oBAAAS,QAAA,GAAC,MAAI,EAACgB,IAAI,CAAC9D,EAAE;kBAAA;oBAAAsC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLrF,OAAA;gBAAIiF,SAAS,EAAC,6BAA6B;gBAAAS,QAAA,eACzC1F,OAAA;kBAAKiF,SAAS,EAAC,mBAAmB;kBAAAS,QAAA,GAC/BX,aAAa,CAAC2B,IAAI,CAAC1B,MAAM,CAAC,eAC3BhF,OAAA;oBAAMiF,SAAS,EAAC,4BAA4B;oBAAAS,QAAA,EAAEgB,IAAI,CAAC1B;kBAAM;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9D;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLrF,OAAA;gBAAIiF,SAAS,EAAC,6BAA6B;gBAAAS,QAAA,eACzC1F,OAAA;kBAAKiF,SAAS,EAAC,yCAAyC;kBAAAS,QAAA,gBACtD1F,OAAA,CAACP,KAAK;oBAACwF,SAAS,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACjCqB,IAAI,CAACC,WAAW,IAAI,CAAC;gBAAA;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLrF,OAAA;gBAAIiF,SAAS,EAAC,6BAA6B;gBAAAS,QAAA,eACzC1F,OAAA;kBAAKiF,SAAS,EAAC,yCAAyC;kBAAAS,QAAA,gBACtD1F,OAAA,CAACN,QAAQ;oBAACuF,SAAS,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACpCC,UAAU,CAACoB,IAAI,CAACE,YAAY,CAAC;gBAAA;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLrF,OAAA;gBAAIiF,SAAS,EAAC,iDAAiD;gBAAAS,QAAA,eAC7D1F,OAAA;kBAAKiF,SAAS,EAAC,gBAAgB;kBAAAS,QAAA,gBAC7B1F,OAAA;oBAAQiF,SAAS,EAAC,mCAAmC;oBAAAS,QAAA,eACnD1F,OAAA,CAACV,GAAG;sBAAC2F,SAAS,EAAC;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CAAC,eACTrF,OAAA;oBAAQiF,SAAS,EAAC,mCAAmC;oBAAAS,QAAA,eACnD1F,OAAA,CAACT,IAAI;sBAAC0F,SAAS,EAAC;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtB,CAAC,eACTrF,OAAA;oBAAQiF,SAAS,EAAC,iCAAiC;oBAAAS,QAAA,eACjD1F,OAAA,CAACR,MAAM;sBAACyF,SAAS,EAAC;oBAAS;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA,GArCEqB,IAAI,CAAC9D,EAAE;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAsCZ,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACnF,EAAA,CAvhBID,gBAAgB;EAAA,QAU8Df,OAAO;AAAA;AAAA2H,EAAA,GAVrF5G,gBAAgB;AAyhBtB,eAAeA,gBAAgB;AAAC,IAAA4G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}