import React, { useState, useEffect, useCallback } from 'react';
import { useForm } from 'react-hook-form';
import toast from 'react-hot-toast';
import {
  ChevronLeft,
  ChevronRight,
  Target,
  Image,
  DollarSign,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import api from '../services/api';

const CampaignWizard = ({ adAccounts, selectedAccount, onClose, onSuccess }) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [loading, setLoading] = useState(false);
  const [targetingOptions, setTargetingOptions] = useState({});
  const [creativeTemplates, setCreativeTemplates] = useState({});
  const [campaignObjectives, setCampaignObjectives] = useState([]);
  const [facebookPages, setFacebookPages] = useState([]);
  const [leadForms, setLeadForms] = useState([]);
  const [leadFormTemplates, setLeadFormTemplates] = useState({});
  
  const { register, handleSubmit, watch, setValue, formState: { errors } } = useForm({
    defaultValues: {
      campaign: {
        name: '',
        objective: 'OUTCOME_TRAFFIC',
        status: 'PAUSED'
      },
      adSet: {
        name: '',
        dailyBudget: '10',
        targeting: {
          age_min: 18,
          age_max: 65,
          genders: [0],
          geo_locations: {
            countries: ['US']
          }
        },
        optimizationGoal: 'LINK_CLICKS',
        billingEvent: 'LINK_CLICKS',
        bidStrategy: 'LOWEST_COST_WITHOUT_CAP',
        status: 'PAUSED'
      },
      ad: {
        name: '',
        creative: {
          object_story_spec: {
            page_id: '',
            link_data: {
              link: '',
              message: '',
              name: '',
              description: '',
              call_to_action: {
                type: 'LEARN_MORE'
              }
            }
          }
        },
        status: 'PAUSED'
      },
      leadForm: {
        useExisting: true,
        existingFormId: '',
        newForm: {
          name: '',
          privacyPolicyUrl: '',
          questions: [
            { type: 'FIRST_NAME' },
            { type: 'LAST_NAME' },
            { type: 'EMAIL' }
          ]
        }
      }
    }
  });

  const watchedValues = watch();

  useEffect(() => {
    loadFormData();
  }, [loadFormData]);

  useEffect(() => {
    if (selectedAccount) {
      loadLeadForms();
    }
  }, [selectedAccount, loadLeadForms]);

  const loadFormData = useCallback(async () => {
    try {
      const [targetingRes, creativeRes, objectivesRes, pagesRes, leadFormTemplatesRes] = await Promise.all([
        api.get('/facebook/targeting-options'),
        api.get('/facebook/creative-templates'),
        api.get('/facebook/campaign-objectives'),
        api.get('/facebook/pages'),
        api.get('/facebook/leadform-templates')
      ]);

      setTargetingOptions(targetingRes.data);
      setCreativeTemplates(creativeRes.data);
      setCampaignObjectives(objectivesRes.data.recommended || []);
      setFacebookPages(pagesRes.data || []);
      setLeadFormTemplates(leadFormTemplatesRes.data || {});

      // Auto-select first page if available
      if (pagesRes.data && pagesRes.data.length > 0) {
        setValue('ad.creative.object_story_spec.page_id', pagesRes.data[0].id);
      }

      // Load lead forms if we have an account selected
      if (selectedAccount) {
        loadLeadForms();
      }
    } catch (error) {
      console.error('Error loading form data:', error);
      toast.error('Failed to load form options');
      // Set fallback empty data to prevent rendering issues
      setTargetingOptions({
        optimizationGoals: [],
        billingEvents: [],
        bidStrategies: []
      });
      setCreativeTemplates({
        callToActionTypes: []
      });
      setCampaignObjectives(['OUTCOME_TRAFFIC', 'OUTCOME_ENGAGEMENT']);
      setFacebookPages([{ id: 'demo_page_123', name: 'Demo Page' }]);
    }
  }, [selectedAccount]);

  const loadLeadForms = useCallback(async () => {
    if (!selectedAccount) return;

    try {
      const response = await api.get(`/facebook/leadforms/${selectedAccount}`);
      setLeadForms(response.data || []);
    } catch (error) {
      console.error('Error loading lead forms:', error);
      setLeadForms([]);
    }
  }, [selectedAccount]);

  const steps = [
    { 
      id: 1, 
      title: 'Campaign Details', 
      icon: Target,
      description: 'Set campaign name and objective'
    },
    { 
      id: 2, 
      title: 'Targeting & Budget', 
      icon: DollarSign,
      description: 'Define audience and budget'
    },
    { 
      id: 3, 
      title: 'Creative & Ad', 
      icon: Image,
      description: 'Create ad content and design'
    },
    { 
      id: 4, 
      title: 'Review & Launch', 
      icon: CheckCircle,
      description: 'Review and create campaign'
    }
  ];

  const nextStep = () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const onSubmit = async (data) => {
    if (currentStep < steps.length) {
      nextStep();
      return;
    }

    // Final submission
    setLoading(true);
    try {
      const isLeadGenCampaign = data.campaign?.objective === 'OUTCOME_LEADS' ||
                                data.campaign?.objective === 'LEAD_GENERATION';

      const payload = {
        adAccountId: selectedAccount,
        campaign: data.campaign,
        adSet: data.adSet,
        ad: data.ad
      };

      // Add lead form data if this is a lead generation campaign
      if (isLeadGenCampaign && data.leadForm) {
        payload.leadForm = data.leadForm;
      }

      console.log('Creating campaign hierarchy:', payload);

      const response = await api.post('/facebook/campaign-hierarchy', payload);

      if (response.data.isLeadGenCampaign) {
        toast.success(`Lead generation campaign created successfully! ${response.data.leadFormId ? 'Lead form ID: ' + response.data.leadFormId : ''}`);
      } else {
        toast.success('Campaign hierarchy created successfully!');
      }

      onSuccess(response.data);
      onClose();
    } catch (error) {
      console.error('Error creating campaign hierarchy:', error);
      toast.error(error.response?.data?.error || 'Failed to create campaign hierarchy');
    } finally {
      setLoading(false);
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900">Campaign Details</h3>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Campaign Name *
              </label>
              <input
                {...register('campaign.name', { required: 'Campaign name is required' })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter campaign name"
              />
              {errors.campaign?.name && (
                <p className="mt-1 text-sm text-red-600">{errors.campaign.name.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Campaign Objective *
              </label>
              <select
                {...register('campaign.objective', { required: 'Objective is required' })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {campaignObjectives?.map(objective => (
                  <option key={objective} value={objective}>
                    {objective.replace('OUTCOME_', '').replace('_', ' ')}
                  </option>
                )) || []}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Campaign Status
              </label>
              <select
                {...register('campaign.status')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option key="PAUSED" value="PAUSED">Paused</option>
                <option key="ACTIVE" value="ACTIVE">Active</option>
              </select>
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900">Targeting & Budget</h3>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Ad Set Name *
              </label>
              <input
                {...register('adSet.name', { required: 'Ad set name is required' })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter ad set name"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Age Min
                </label>
                <input
                  {...register('adSet.targeting.age_min', { 
                    required: 'Age min is required',
                    min: { value: 18, message: 'Minimum age is 18' }
                  })}
                  type="number"
                  min="18"
                  max="65"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Age Max
                </label>
                <input
                  {...register('adSet.targeting.age_max', { 
                    required: 'Age max is required',
                    max: { value: 65, message: 'Maximum age is 65' }
                  })}
                  type="number"
                  min="18"
                  max="65"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Daily Budget (USD) *
              </label>
              <input
                {...register('adSet.dailyBudget', { 
                  required: 'Daily budget is required',
                  min: { value: 1, message: 'Minimum budget is $1' }
                })}
                type="number"
                min="1"
                step="0.01"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="10.00"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Optimization Goal
              </label>
              <select
                {...register('adSet.optimizationGoal')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {targetingOptions?.optimizationGoals?.map(goal => (
                  <option key={goal.value} value={goal.value}>
                    {goal.label}
                  </option>
                )) || []}
              </select>
            </div>
          </div>
        );

      case 3:
        const isLeadGenCampaign = watchedValues.campaign?.objective === 'OUTCOME_LEADS' ||
                                  watchedValues.campaign?.objective === 'LEAD_GENERATION';

        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900">Creative & Ad</h3>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Ad Name *
              </label>
              <input
                {...register('ad.name', { required: 'Ad name is required' })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter ad name"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Facebook Page *
              </label>
              <select
                {...register('ad.creative.object_story_spec.page_id', { required: 'Facebook page is required' })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {facebookPages?.map(page => (
                  <option key={page.id} value={page.id}>
                    {page.name}
                  </option>
                )) || []}
              </select>
            </div>

            {isLeadGenCampaign ? (
              // Lead Generation Creative
              <div className="space-y-4">
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h4 className="text-sm font-medium text-blue-800 mb-2">Lead Generation Campaign</h4>
                  <p className="text-sm text-blue-700">
                    This campaign will use lead forms to collect user information directly on Facebook.
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    Lead Form Setup
                  </label>
                  <div className="space-y-3">
                    <label className="flex items-center">
                      <input
                        type="radio"
                        {...register('leadForm.useExisting')}
                        value={true}
                        className="mr-2"
                      />
                      <span className="text-sm text-gray-700">Use existing lead form</span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="radio"
                        {...register('leadForm.useExisting')}
                        value={false}
                        className="mr-2"
                      />
                      <span className="text-sm text-gray-700">Create new lead form</span>
                    </label>
                  </div>
                </div>

                {watchedValues.leadForm?.useExisting === 'true' || watchedValues.leadForm?.useExisting === true ? (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Select Lead Form *
                    </label>
                    <select
                      {...register('leadForm.existingFormId', { required: 'Lead form is required' })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="">Select a lead form</option>
                      {leadForms?.map(form => (
                        <option key={form.id} value={form.id}>
                          {form.name} ({form.leads_count || 0} leads)
                        </option>
                      )) || []}
                    </select>
                    {leadForms?.length === 0 && (
                      <p className="mt-1 text-sm text-yellow-600">
                        No lead forms found. Create one first or choose "Create new lead form" option.
                      </p>
                    )}
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Lead Form Name *
                      </label>
                      <input
                        {...register('leadForm.newForm.name', { required: 'Lead form name is required' })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="Enter lead form name"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Privacy Policy URL *
                      </label>
                      <input
                        {...register('leadForm.newForm.privacyPolicyUrl', {
                          required: 'Privacy policy URL is required',
                          pattern: {
                            value: /^https?:\/\/.+/,
                            message: 'Please enter a valid URL'
                          }
                        })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="https://example.com/privacy-policy"
                      />
                    </div>
                  </div>
                )}

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Primary Text *
                  </label>
                  <textarea
                    {...register('ad.creative.object_story_spec.link_data.message', {
                      required: 'Primary text is required'
                    })}
                    rows="3"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Write compelling ad copy that encourages users to fill out your lead form..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Headline *
                  </label>
                  <input
                    {...register('ad.creative.object_story_spec.link_data.name', {
                      required: 'Headline is required'
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter compelling headline"
                  />
                </div>
              </div>
            ) : (
              // Regular Link Creative
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Destination URL *
                  </label>
                  <input
                    {...register('ad.creative.object_story_spec.link_data.link', {
                      required: 'Destination URL is required',
                      pattern: {
                        value: /^https?:\/\/.+/,
                        message: 'Please enter a valid URL'
                      }
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="https://example.com"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Primary Text *
                  </label>
                  <textarea
                    {...register('ad.creative.object_story_spec.link_data.message', {
                      required: 'Primary text is required'
                    })}
                    rows="3"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Write compelling ad copy..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Headline *
                  </label>
                  <input
                    {...register('ad.creative.object_story_spec.link_data.name', {
                      required: 'Headline is required'
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter headline"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Description
                  </label>
                  <input
                    {...register('ad.creative.object_story_spec.link_data.description')}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter description"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Call to Action
                  </label>
                  <select
                    {...register('ad.creative.object_story_spec.link_data.call_to_action.type')}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    {creativeTemplates?.callToActionTypes?.map(cta => (
                      <option key={cta.value} value={cta.value}>
                        {cta.label}
                      </option>
                    )) || []}
                  </select>
                </div>
              </div>
            )}
          </div>
        );

      case 4:
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900">Review & Launch</h3>
            
            <div className="bg-gray-50 p-4 rounded-lg space-y-4">
              <div>
                <h4 className="font-medium text-gray-900">Campaign</h4>
                <p className="text-sm text-gray-600">
                  {watchedValues.campaign?.name} - {watchedValues.campaign?.objective}
                </p>
              </div>
              
              <div>
                <h4 className="font-medium text-gray-900">Ad Set</h4>
                <p className="text-sm text-gray-600">
                  {watchedValues.adSet?.name} - ${watchedValues.adSet?.dailyBudget}/day
                </p>
                <p className="text-sm text-gray-600">
                  Ages {watchedValues.adSet?.targeting?.age_min}-{watchedValues.adSet?.targeting?.age_max}
                </p>
              </div>
              
              <div>
                <h4 className="font-medium text-gray-900">Ad</h4>
                <p className="text-sm text-gray-600">
                  {watchedValues.ad?.name}
                </p>
                <p className="text-sm text-gray-600">
                  {watchedValues.ad?.creative?.object_story_spec?.link_data?.message?.substring(0, 100)}...
                </p>
              </div>
            </div>

            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex">
                <AlertCircle className="h-5 w-5 text-yellow-400 mr-2 mt-0.5" />
                <div>
                  <h4 className="text-sm font-medium text-yellow-800">Ready to Create</h4>
                  <p className="text-sm text-yellow-700">
                    This will create a complete campaign hierarchy with campaign, ad set, and ad.
                    All entities will be created in PAUSED status for your review.
                  </p>
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-gray-900">
              Create Campaign Hierarchy
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              ×
            </button>
          </div>
          
          {/* Progress Steps */}
          <div className="mt-4">
            <div className="flex items-center justify-between">
              {steps.map((step, index) => (
                <div key={step.id} className="flex items-center">
                  <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
                    currentStep >= step.id 
                      ? 'bg-blue-600 text-white' 
                      : 'bg-gray-200 text-gray-600'
                  }`}>
                    {currentStep > step.id ? (
                      <CheckCircle className="h-5 w-5" />
                    ) : (
                      <step.icon className="h-4 w-4" />
                    )}
                  </div>
                  <div className="ml-2 hidden sm:block">
                    <p className={`text-sm font-medium ${
                      currentStep >= step.id ? 'text-blue-600' : 'text-gray-500'
                    }`}>
                      {step.title}
                    </p>
                  </div>
                  {index < steps.length - 1 && (
                    <div className={`w-12 h-0.5 mx-4 ${
                      currentStep > step.id ? 'bg-blue-600' : 'bg-gray-200'
                    }`} />
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Content */}
        <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col h-full">
          <div className="flex-1 px-6 py-6 overflow-y-auto">
            {renderStepContent()}
          </div>

          {/* Footer */}
          <div className="px-6 py-4 border-t border-gray-200 flex justify-between">
            <button
              type="button"
              onClick={prevStep}
              disabled={currentStep === 1}
              className="flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ChevronLeft className="h-4 w-4 mr-1" />
              Previous
            </button>

            <button
              type="submit"
              disabled={loading}
              className="flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? (
                'Creating...'
              ) : currentStep === steps.length ? (
                'Create Campaign Hierarchy'
              ) : (
                <>
                  Next
                  <ChevronRight className="h-4 w-4 ml-1" />
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CampaignWizard;
