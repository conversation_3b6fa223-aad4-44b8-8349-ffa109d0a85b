{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FB API SCRAPER\\\\pressure-max-frontend\\\\src\\\\App.js\";\nimport React from 'react';\nimport { Toaster } from 'react-hot-toast';\nimport { AuthProvider } from './contexts/AuthContext';\nimport { FacebookProvider } from './contexts/FacebookContext';\nimport AuthSection from './components/AuthSection';\nimport FacebookConnectionSection from './components/FacebookConnectionSection';\nimport CampaignSection from './components/CampaignSection';\nimport LeadFormsSection from './components/LeadFormsSection';\nimport ApiTestingSection from './components/ApiTestingSection';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(AuthProvider, {\n    children: /*#__PURE__*/_jsxDEV(FacebookProvider, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"App\",\n        children: [/*#__PURE__*/_jsxDEV(\"header\", {\n          className: \"app-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            children: \"\\uD83D\\uDE80 Pressure Max API Tester\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 18,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Test interface for the Pressure Max API backend\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 19,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n          className: \"app-main\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"sections-container\",\n            children: [/*#__PURE__*/_jsxDEV(\"section\", {\n              className: \"section\",\n              children: /*#__PURE__*/_jsxDEV(AuthSection, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 25,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 24,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n              className: \"section\",\n              children: /*#__PURE__*/_jsxDEV(FacebookConnectionSection, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 29,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 28,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n              className: \"section\",\n              children: /*#__PURE__*/_jsxDEV(CampaignSection, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 33,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 32,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n              className: \"section\",\n              children: /*#__PURE__*/_jsxDEV(LeadFormsSection, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 37,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 36,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n              className: \"section\",\n              children: /*#__PURE__*/_jsxDEV(ApiTestingSection, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 41,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 40,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 23,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n          className: \"app-footer\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Pressure Max API Testing Interface - Built with React\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"Backend API: \", /*#__PURE__*/_jsxDEV(\"code\", {\n              children: \"http://localhost:3000\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 48,\n              columnNumber: 27\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Toaster, {\n          position: \"top-right\",\n          toastOptions: {\n            duration: 4000,\n            style: {\n              background: '#363636',\n              color: '#fff'\n            },\n            success: {\n              duration: 3000,\n              iconTheme: {\n                primary: '#4ade80',\n                secondary: '#fff'\n              }\n            },\n            error: {\n              duration: 5000,\n              iconTheme: {\n                primary: '#ef4444',\n                secondary: '#fff'\n              }\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 14,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "Toaster", "<PERSON>th<PERSON><PERSON><PERSON>", "FacebookProvider", "AuthSection", "FacebookConnectionSection", "CampaignSection", "LeadFormsSection", "ApiTestingSection", "jsxDEV", "_jsxDEV", "App", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "position", "toastOptions", "duration", "style", "background", "color", "success", "iconTheme", "primary", "secondary", "error", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/FB API SCRAPER/pressure-max-frontend/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { Toaster } from 'react-hot-toast';\nimport { AuthProvider } from './contexts/AuthContext';\nimport { FacebookProvider } from './contexts/FacebookContext';\nimport AuthSection from './components/AuthSection';\nimport FacebookConnectionSection from './components/FacebookConnectionSection';\nimport CampaignSection from './components/CampaignSection';\nimport LeadFormsSection from './components/LeadFormsSection';\nimport ApiTestingSection from './components/ApiTestingSection';\nimport './App.css';\n\nfunction App() {\n  return (\n    <AuthProvider>\n      <FacebookProvider>\n        <div className=\"App\">\n        <header className=\"app-header\">\n          <h1>🚀 Pressure Max API Tester</h1>\n          <p>Test interface for the Pressure Max API backend</p>\n        </header>\n\n        <main className=\"app-main\">\n          <div className=\"sections-container\">\n            <section className=\"section\">\n              <AuthSection />\n            </section>\n\n            <section className=\"section\">\n              <FacebookConnectionSection />\n            </section>\n\n            <section className=\"section\">\n              <CampaignSection />\n            </section>\n\n            <section className=\"section\">\n              <LeadFormsSection />\n            </section>\n\n            <section className=\"section\">\n              <ApiTestingSection />\n            </section>\n          </div>\n        </main>\n\n        <footer className=\"app-footer\">\n          <p>Pressure Max API Testing Interface - Built with React</p>\n          <p>Backend API: <code>http://localhost:3000</code></p>\n        </footer>\n\n        <Toaster \n          position=\"top-right\"\n          toastOptions={{\n            duration: 4000,\n            style: {\n              background: '#363636',\n              color: '#fff',\n            },\n            success: {\n              duration: 3000,\n              iconTheme: {\n                primary: '#4ade80',\n                secondary: '#fff',\n              },\n            },\n            error: {\n              duration: 5000,\n              iconTheme: {\n                primary: '#ef4444',\n                secondary: '#fff',\n              },\n            },\n          }}\n        />\n        </div>\n      </FacebookProvider>\n    </AuthProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,iBAAiB;AACzC,SAASC,YAAY,QAAQ,wBAAwB;AACrD,SAASC,gBAAgB,QAAQ,4BAA4B;AAC7D,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,yBAAyB,MAAM,wCAAwC;AAC9E,OAAOC,eAAe,MAAM,8BAA8B;AAC1D,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,OAAOC,iBAAiB,MAAM,gCAAgC;AAC9D,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACR,YAAY;IAAAU,QAAA,eACXF,OAAA,CAACP,gBAAgB;MAAAS,QAAA,eACfF,OAAA;QAAKG,SAAS,EAAC,KAAK;QAAAD,QAAA,gBACpBF,OAAA;UAAQG,SAAS,EAAC,YAAY;UAAAD,QAAA,gBAC5BF,OAAA;YAAAE,QAAA,EAAI;UAA0B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnCP,OAAA;YAAAE,QAAA,EAAG;UAA+C;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC,eAETP,OAAA;UAAMG,SAAS,EAAC,UAAU;UAAAD,QAAA,eACxBF,OAAA;YAAKG,SAAS,EAAC,oBAAoB;YAAAD,QAAA,gBACjCF,OAAA;cAASG,SAAS,EAAC,SAAS;cAAAD,QAAA,eAC1BF,OAAA,CAACN,WAAW;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eAEVP,OAAA;cAASG,SAAS,EAAC,SAAS;cAAAD,QAAA,eAC1BF,OAAA,CAACL,yBAAyB;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC,eAEVP,OAAA;cAASG,SAAS,EAAC,SAAS;cAAAD,QAAA,eAC1BF,OAAA,CAACJ,eAAe;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eAEVP,OAAA;cAASG,SAAS,EAAC,SAAS;cAAAD,QAAA,eAC1BF,OAAA,CAACH,gBAAgB;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC,eAEVP,OAAA;cAASG,SAAS,EAAC,SAAS;cAAAD,QAAA,eAC1BF,OAAA,CAACF,iBAAiB;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEPP,OAAA;UAAQG,SAAS,EAAC,YAAY;UAAAD,QAAA,gBAC5BF,OAAA;YAAAE,QAAA,EAAG;UAAqD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC5DP,OAAA;YAAAE,QAAA,GAAG,eAAa,eAAAF,OAAA;cAAAE,QAAA,EAAM;YAAqB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC,eAETP,OAAA,CAACT,OAAO;UACNiB,QAAQ,EAAC,WAAW;UACpBC,YAAY,EAAE;YACZC,QAAQ,EAAE,IAAI;YACdC,KAAK,EAAE;cACLC,UAAU,EAAE,SAAS;cACrBC,KAAK,EAAE;YACT,CAAC;YACDC,OAAO,EAAE;cACPJ,QAAQ,EAAE,IAAI;cACdK,SAAS,EAAE;gBACTC,OAAO,EAAE,SAAS;gBAClBC,SAAS,EAAE;cACb;YACF,CAAC;YACDC,KAAK,EAAE;cACLR,QAAQ,EAAE,IAAI;cACdK,SAAS,EAAE;gBACTC,OAAO,EAAE,SAAS;gBAClBC,SAAS,EAAE;cACb;YACF;UACF;QAAE;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACU;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEnB;AAACY,EAAA,GAnEQlB,GAAG;AAqEZ,eAAeA,GAAG;AAAC,IAAAkB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}