{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FB API SCRAPER\\\\pressure-max-frontend\\\\src\\\\components\\\\CampaignWizard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useForm } from 'react-hook-form';\nimport toast from 'react-hot-toast';\nimport { ChevronLeft, ChevronRight, Target, Image, DollarSign, Calendar, CheckCircle, AlertCircle } from 'lucide-react';\nimport api from '../services/api';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst CampaignWizard = ({\n  adAccounts,\n  selectedAccount,\n  onClose,\n  onSuccess\n}) => {\n  _s();\n  const [currentStep, setCurrentStep] = useState(1);\n  const [loading, setLoading] = useState(false);\n  const [targetingOptions, setTargetingOptions] = useState({});\n  const [creativeTemplates, setCreativeTemplates] = useState({});\n  const [campaignObjectives, setCampaignObjectives] = useState([]);\n  const [facebookPages, setFacebookPages] = useState([]);\n  const [leadForms, setLeadForms] = useState([]);\n  const [leadFormTemplates, setLeadFormTemplates] = useState({});\n  const {\n    register,\n    handleSubmit,\n    watch,\n    setValue,\n    formState: {\n      errors\n    }\n  } = useForm({\n    defaultValues: {\n      campaign: {\n        name: '',\n        objective: 'OUTCOME_TRAFFIC',\n        status: 'PAUSED'\n      },\n      adSet: {\n        name: '',\n        dailyBudget: '10',\n        targeting: {\n          age_min: 18,\n          age_max: 65,\n          genders: [0],\n          geo_locations: {\n            countries: ['US']\n          }\n        },\n        optimizationGoal: 'LINK_CLICKS',\n        billingEvent: 'LINK_CLICKS',\n        bidStrategy: 'LOWEST_COST_WITHOUT_CAP',\n        status: 'PAUSED'\n      },\n      ad: {\n        name: '',\n        creative: {\n          object_story_spec: {\n            page_id: '',\n            link_data: {\n              link: '',\n              message: '',\n              name: '',\n              description: '',\n              call_to_action: {\n                type: 'LEARN_MORE'\n              }\n            }\n          }\n        },\n        status: 'PAUSED'\n      },\n      leadForm: {\n        useExisting: true,\n        existingFormId: '',\n        newForm: {\n          name: '',\n          privacyPolicyUrl: '',\n          questions: [{\n            type: 'FIRST_NAME'\n          }, {\n            type: 'LAST_NAME'\n          }, {\n            type: 'EMAIL'\n          }]\n        }\n      }\n    }\n  });\n  const watchedValues = watch();\n  useEffect(() => {\n    loadFormData();\n  }, []);\n  useEffect(() => {\n    if (selectedAccount) {\n      loadLeadForms();\n    }\n  }, [selectedAccount]);\n  const loadFormData = async () => {\n    try {\n      const [targetingRes, creativeRes, objectivesRes, pagesRes, leadFormTemplatesRes] = await Promise.all([api.get('/facebook/targeting-options'), api.get('/facebook/creative-templates'), api.get('/facebook/campaign-objectives'), api.get('/facebook/pages'), api.get('/facebook/leadform-templates')]);\n      setTargetingOptions(targetingRes.data);\n      setCreativeTemplates(creativeRes.data);\n      setCampaignObjectives(objectivesRes.data.recommended || []);\n      setFacebookPages(pagesRes.data || []);\n      setLeadFormTemplates(leadFormTemplatesRes.data || {});\n\n      // Auto-select first page if available\n      if (pagesRes.data && pagesRes.data.length > 0) {\n        setValue('ad.creative.object_story_spec.page_id', pagesRes.data[0].id);\n      }\n\n      // Load lead forms if we have an account selected\n      if (selectedAccount) {\n        loadLeadForms();\n      }\n    } catch (error) {\n      console.error('Error loading form data:', error);\n      toast.error('Failed to load form options');\n      // Set fallback empty data to prevent rendering issues\n      setTargetingOptions({\n        optimizationGoals: [],\n        billingEvents: [],\n        bidStrategies: []\n      });\n      setCreativeTemplates({\n        callToActionTypes: []\n      });\n      setCampaignObjectives(['OUTCOME_TRAFFIC', 'OUTCOME_ENGAGEMENT']);\n      setFacebookPages([{\n        id: 'demo_page_123',\n        name: 'Demo Page'\n      }]);\n    }\n  };\n  const loadLeadForms = async () => {\n    if (!selectedAccount) return;\n    try {\n      const response = await api.get(`/facebook/leadforms/${selectedAccount}`);\n      setLeadForms(response.data || []);\n    } catch (error) {\n      console.error('Error loading lead forms:', error);\n      setLeadForms([]);\n    }\n  };\n  const steps = [{\n    id: 1,\n    title: 'Campaign Details',\n    icon: Target,\n    description: 'Set campaign name and objective'\n  }, {\n    id: 2,\n    title: 'Targeting & Budget',\n    icon: DollarSign,\n    description: 'Define audience and budget'\n  }, {\n    id: 3,\n    title: 'Creative & Ad',\n    icon: Image,\n    description: 'Create ad content and design'\n  }, {\n    id: 4,\n    title: 'Review & Launch',\n    icon: CheckCircle,\n    description: 'Review and create campaign'\n  }];\n  const nextStep = () => {\n    if (currentStep < steps.length) {\n      setCurrentStep(currentStep + 1);\n    }\n  };\n  const prevStep = () => {\n    if (currentStep > 1) {\n      setCurrentStep(currentStep - 1);\n    }\n  };\n  const onSubmit = async data => {\n    if (currentStep < steps.length) {\n      nextStep();\n      return;\n    }\n\n    // Final submission\n    setLoading(true);\n    try {\n      var _data$campaign, _data$campaign2;\n      const isLeadGenCampaign = ((_data$campaign = data.campaign) === null || _data$campaign === void 0 ? void 0 : _data$campaign.objective) === 'OUTCOME_LEADS' || ((_data$campaign2 = data.campaign) === null || _data$campaign2 === void 0 ? void 0 : _data$campaign2.objective) === 'LEAD_GENERATION';\n      const payload = {\n        adAccountId: selectedAccount,\n        campaign: data.campaign,\n        adSet: data.adSet,\n        ad: data.ad\n      };\n\n      // Add lead form data if this is a lead generation campaign\n      if (isLeadGenCampaign && data.leadForm) {\n        payload.leadForm = data.leadForm;\n      }\n      console.log('Creating campaign hierarchy:', payload);\n      const response = await api.post('/facebook/campaign-hierarchy', payload);\n      if (response.data.isLeadGenCampaign) {\n        toast.success(`Lead generation campaign created successfully! ${response.data.leadFormId ? 'Lead form ID: ' + response.data.leadFormId : ''}`);\n      } else {\n        toast.success('Campaign hierarchy created successfully!');\n      }\n      onSuccess(response.data);\n      onClose();\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Error creating campaign hierarchy:', error);\n      toast.error(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.error) || 'Failed to create campaign hierarchy');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const renderStepContent = () => {\n    var _errors$campaign, _targetingOptions$opt, _watchedValues$campai, _watchedValues$campai2, _watchedValues$leadFo, _watchedValues$leadFo2, _creativeTemplates$ca, _watchedValues$campai3, _watchedValues$campai4, _watchedValues$adSet, _watchedValues$adSet2, _watchedValues$adSet3, _watchedValues$adSet4, _watchedValues$adSet5, _watchedValues$adSet6, _watchedValues$ad, _watchedValues$ad2, _watchedValues$ad2$cr, _watchedValues$ad2$cr2, _watchedValues$ad2$cr3, _watchedValues$ad2$cr4;\n    switch (currentStep) {\n      case 1:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-900\",\n            children: \"Campaign Details\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Campaign Name *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              ...register('campaign.name', {\n                required: 'Campaign name is required'\n              }),\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n              placeholder: \"Enter campaign name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 15\n            }, this), ((_errors$campaign = errors.campaign) === null || _errors$campaign === void 0 ? void 0 : _errors$campaign.name) && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-red-600\",\n              children: errors.campaign.name.message\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Campaign Objective *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              ...register('campaign.objective', {\n                required: 'Objective is required'\n              }),\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n              children: (campaignObjectives === null || campaignObjectives === void 0 ? void 0 : campaignObjectives.map(objective => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: objective,\n                children: objective.replace('OUTCOME_', '').replace('_', ' ')\n              }, objective, false, {\n                fileName: _jsxFileName,\n                lineNumber: 262,\n                columnNumber: 19\n              }, this))) || []\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Campaign Status\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              ...register('campaign.status'),\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"PAUSED\",\n                children: \"Paused\"\n              }, \"PAUSED\", false, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"ACTIVE\",\n                children: \"Active\"\n              }, \"ACTIVE\", false, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 11\n        }, this);\n      case 2:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-900\",\n            children: \"Targeting & Budget\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Ad Set Name *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              ...register('adSet.name', {\n                required: 'Ad set name is required'\n              }),\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n              placeholder: \"Enter ad set name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-2 gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Age Min\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                ...register('adSet.targeting.age_min', {\n                  required: 'Age min is required',\n                  min: {\n                    value: 18,\n                    message: 'Minimum age is 18'\n                  }\n                }),\n                type: \"number\",\n                min: \"18\",\n                max: \"65\",\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Age Max\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                ...register('adSet.targeting.age_max', {\n                  required: 'Age max is required',\n                  max: {\n                    value: 65,\n                    message: 'Maximum age is 65'\n                  }\n                }),\n                type: \"number\",\n                min: \"18\",\n                max: \"65\",\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 316,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Daily Budget (USD) *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              ...register('adSet.dailyBudget', {\n                required: 'Daily budget is required',\n                min: {\n                  value: 1,\n                  message: 'Minimum budget is $1'\n                }\n              }),\n              type: \"number\",\n              min: \"1\",\n              step: \"0.01\",\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n              placeholder: \"10.00\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Optimization Goal\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 351,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              ...register('adSet.optimizationGoal'),\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n              children: (targetingOptions === null || targetingOptions === void 0 ? void 0 : (_targetingOptions$opt = targetingOptions.optimizationGoals) === null || _targetingOptions$opt === void 0 ? void 0 : _targetingOptions$opt.map(goal => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: goal.value,\n                children: goal.label\n              }, goal.value, false, {\n                fileName: _jsxFileName,\n                lineNumber: 359,\n                columnNumber: 19\n              }, this))) || []\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 354,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 11\n        }, this);\n      case 3:\n        const isLeadGenCampaign = ((_watchedValues$campai = watchedValues.campaign) === null || _watchedValues$campai === void 0 ? void 0 : _watchedValues$campai.objective) === 'OUTCOME_LEADS' || ((_watchedValues$campai2 = watchedValues.campaign) === null || _watchedValues$campai2 === void 0 ? void 0 : _watchedValues$campai2.objective) === 'LEAD_GENERATION';\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-900\",\n            children: \"Creative & Ad\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 374,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Ad Name *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 377,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              ...register('ad.name', {\n                required: 'Ad name is required'\n              }),\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n              placeholder: \"Enter ad name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 376,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-2\",\n              children: \"Facebook Page *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 388,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              ...register('ad.creative.object_story_spec.page_id', {\n                required: 'Facebook page is required'\n              }),\n              className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n              children: (facebookPages === null || facebookPages === void 0 ? void 0 : facebookPages.map(page => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: page.id,\n                children: page.name\n              }, page.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 396,\n                columnNumber: 19\n              }, this))) || []\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 391,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 387,\n            columnNumber: 13\n          }, this), isLeadGenCampaign ?\n          /*#__PURE__*/\n          // Lead Generation Creative\n          _jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"text-sm font-medium text-blue-800 mb-2\",\n                children: \"Lead Generation Campaign\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 407,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-blue-700\",\n                children: \"This campaign will use lead forms to collect user information directly on Facebook.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 408,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 406,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-3\",\n                children: \"Lead Form Setup\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 414,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"radio\",\n                    ...register('leadForm.useExisting'),\n                    value: true,\n                    className: \"mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 419,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm text-gray-700\",\n                    children: \"Use existing lead form\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 425,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 418,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"radio\",\n                    ...register('leadForm.useExisting'),\n                    value: false,\n                    className: \"mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 428,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm text-gray-700\",\n                    children: \"Create new lead form\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 434,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 427,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 417,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 413,\n              columnNumber: 17\n            }, this), ((_watchedValues$leadFo = watchedValues.leadForm) === null || _watchedValues$leadFo === void 0 ? void 0 : _watchedValues$leadFo.useExisting) === 'true' || ((_watchedValues$leadFo2 = watchedValues.leadForm) === null || _watchedValues$leadFo2 === void 0 ? void 0 : _watchedValues$leadFo2.useExisting) === true ? /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Select Lead Form *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 441,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                ...register('leadForm.existingFormId', {\n                  required: 'Lead form is required'\n                }),\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select a lead form\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 448,\n                  columnNumber: 23\n                }, this), (leadForms === null || leadForms === void 0 ? void 0 : leadForms.map(form => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: form.id,\n                  children: [form.name, \" (\", form.leads_count || 0, \" leads)\"]\n                }, form.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 450,\n                  columnNumber: 25\n                }, this))) || []]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 444,\n                columnNumber: 21\n              }, this), (leadForms === null || leadForms === void 0 ? void 0 : leadForms.length) === 0 && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"mt-1 text-sm text-yellow-600\",\n                children: \"No lead forms found. Create one first or choose \\\"Create new lead form\\\" option.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 456,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 440,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Lead Form Name *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 464,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  ...register('leadForm.newForm.name', {\n                    required: 'Lead form name is required'\n                  }),\n                  className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                  placeholder: \"Enter lead form name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 467,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 463,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Privacy Policy URL *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 474,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  ...register('leadForm.newForm.privacyPolicyUrl', {\n                    required: 'Privacy policy URL is required',\n                    pattern: {\n                      value: /^https?:\\/\\/.+/,\n                      message: 'Please enter a valid URL'\n                    }\n                  }),\n                  className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                  placeholder: \"https://example.com/privacy-policy\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 477,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 473,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 462,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Primary Text *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 493,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                ...register('ad.creative.object_story_spec.link_data.message', {\n                  required: 'Primary text is required'\n                }),\n                rows: \"3\",\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                placeholder: \"Write compelling ad copy that encourages users to fill out your lead form...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 496,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 492,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Headline *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 507,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                ...register('ad.creative.object_story_spec.link_data.name', {\n                  required: 'Headline is required'\n                }),\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                placeholder: \"Enter compelling headline\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 510,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 506,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 405,\n            columnNumber: 15\n          }, this) :\n          /*#__PURE__*/\n          // Regular Link Creative\n          _jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Destination URL *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 523,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                ...register('ad.creative.object_story_spec.link_data.link', {\n                  required: 'Destination URL is required',\n                  pattern: {\n                    value: /^https?:\\/\\/.+/,\n                    message: 'Please enter a valid URL'\n                  }\n                }),\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                placeholder: \"https://example.com\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 526,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 522,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Primary Text *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 540,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                ...register('ad.creative.object_story_spec.link_data.message', {\n                  required: 'Primary text is required'\n                }),\n                rows: \"3\",\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                placeholder: \"Write compelling ad copy...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 543,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 539,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Headline *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 554,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                ...register('ad.creative.object_story_spec.link_data.name', {\n                  required: 'Headline is required'\n                }),\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                placeholder: \"Enter headline\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 557,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 553,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Description\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 567,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                ...register('ad.creative.object_story_spec.link_data.description'),\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                placeholder: \"Enter description\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 570,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 566,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Call to Action\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 578,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                ...register('ad.creative.object_story_spec.link_data.call_to_action.type'),\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                children: (creativeTemplates === null || creativeTemplates === void 0 ? void 0 : (_creativeTemplates$ca = creativeTemplates.callToActionTypes) === null || _creativeTemplates$ca === void 0 ? void 0 : _creativeTemplates$ca.map(cta => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: cta.value,\n                  children: cta.label\n                }, cta.value, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 586,\n                  columnNumber: 23\n                }, this))) || []\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 581,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 577,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 521,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 373,\n          columnNumber: 11\n        }, this);\n      case 4:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-900\",\n            children: \"Review & Launch\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 600,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gray-50 p-4 rounded-lg space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"font-medium text-gray-900\",\n                children: \"Campaign\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 604,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600\",\n                children: [(_watchedValues$campai3 = watchedValues.campaign) === null || _watchedValues$campai3 === void 0 ? void 0 : _watchedValues$campai3.name, \" - \", (_watchedValues$campai4 = watchedValues.campaign) === null || _watchedValues$campai4 === void 0 ? void 0 : _watchedValues$campai4.objective]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 605,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 603,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"font-medium text-gray-900\",\n                children: \"Ad Set\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 611,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600\",\n                children: [(_watchedValues$adSet = watchedValues.adSet) === null || _watchedValues$adSet === void 0 ? void 0 : _watchedValues$adSet.name, \" - $\", (_watchedValues$adSet2 = watchedValues.adSet) === null || _watchedValues$adSet2 === void 0 ? void 0 : _watchedValues$adSet2.dailyBudget, \"/day\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 612,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600\",\n                children: [\"Ages \", (_watchedValues$adSet3 = watchedValues.adSet) === null || _watchedValues$adSet3 === void 0 ? void 0 : (_watchedValues$adSet4 = _watchedValues$adSet3.targeting) === null || _watchedValues$adSet4 === void 0 ? void 0 : _watchedValues$adSet4.age_min, \"-\", (_watchedValues$adSet5 = watchedValues.adSet) === null || _watchedValues$adSet5 === void 0 ? void 0 : (_watchedValues$adSet6 = _watchedValues$adSet5.targeting) === null || _watchedValues$adSet6 === void 0 ? void 0 : _watchedValues$adSet6.age_max]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 615,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 610,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"font-medium text-gray-900\",\n                children: \"Ad\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 621,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600\",\n                children: (_watchedValues$ad = watchedValues.ad) === null || _watchedValues$ad === void 0 ? void 0 : _watchedValues$ad.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 622,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600\",\n                children: [(_watchedValues$ad2 = watchedValues.ad) === null || _watchedValues$ad2 === void 0 ? void 0 : (_watchedValues$ad2$cr = _watchedValues$ad2.creative) === null || _watchedValues$ad2$cr === void 0 ? void 0 : (_watchedValues$ad2$cr2 = _watchedValues$ad2$cr.object_story_spec) === null || _watchedValues$ad2$cr2 === void 0 ? void 0 : (_watchedValues$ad2$cr3 = _watchedValues$ad2$cr2.link_data) === null || _watchedValues$ad2$cr3 === void 0 ? void 0 : (_watchedValues$ad2$cr4 = _watchedValues$ad2$cr3.message) === null || _watchedValues$ad2$cr4 === void 0 ? void 0 : _watchedValues$ad2$cr4.substring(0, 100), \"...\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 625,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 620,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 602,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex\",\n              children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                className: \"h-5 w-5 text-yellow-400 mr-2 mt-0.5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 633,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"text-sm font-medium text-yellow-800\",\n                  children: \"Ready to Create\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 635,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-yellow-700\",\n                  children: \"This will create a complete campaign hierarchy with campaign, ad set, and ad. All entities will be created in PAUSED status for your review.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 636,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 634,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 632,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 631,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 599,\n          columnNumber: 11\n        }, this);\n      default:\n        return null;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-6 py-4 border-b border-gray-200\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-semibold text-gray-900\",\n            children: \"Create Campaign Hierarchy\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 657,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onClose,\n            className: \"text-gray-400 hover:text-gray-600\",\n            children: \"\\xD7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 660,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 656,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: steps.map((step, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `flex items-center justify-center w-8 h-8 rounded-full ${currentStep >= step.id ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-600'}`,\n                children: currentStep > step.id ? /*#__PURE__*/_jsxDEV(CheckCircle, {\n                  className: \"h-5 w-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 679,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(step.icon, {\n                  className: \"h-4 w-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 681,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 673,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"ml-2 hidden sm:block\",\n                children: /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: `text-sm font-medium ${currentStep >= step.id ? 'text-blue-600' : 'text-gray-500'}`,\n                  children: step.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 685,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 684,\n                columnNumber: 19\n              }, this), index < steps.length - 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-12 h-0.5 mx-4 ${currentStep > step.id ? 'bg-blue-600' : 'bg-gray-200'}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 692,\n                columnNumber: 21\n              }, this)]\n            }, step.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 672,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 670,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 669,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 655,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit(onSubmit),\n        className: \"flex flex-col h-full\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1 px-6 py-6 overflow-y-auto\",\n          children: renderStepContent()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 704,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-6 py-4 border-t border-gray-200 flex justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: prevStep,\n            disabled: currentStep === 1,\n            className: \"flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\",\n            children: [/*#__PURE__*/_jsxDEV(ChevronLeft, {\n              className: \"h-4 w-4 mr-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 716,\n              columnNumber: 15\n            }, this), \"Previous\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 710,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            disabled: loading,\n            className: \"flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed\",\n            children: loading ? 'Creating...' : currentStep === steps.length ? 'Create Campaign Hierarchy' : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [\"Next\", /*#__PURE__*/_jsxDEV(ChevronRight, {\n                className: \"h-4 w-4 ml-1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 732,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 720,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 709,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 703,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 653,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 652,\n    columnNumber: 5\n  }, this);\n};\n_s(CampaignWizard, \"xSbf3p829MYVHFrEXvkc69pGrPA=\", false, function () {\n  return [useForm];\n});\n_c = CampaignWizard;\nexport default CampaignWizard;\nvar _c;\n$RefreshReg$(_c, \"CampaignWizard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useForm", "toast", "ChevronLeft", "ChevronRight", "Target", "Image", "DollarSign", "Calendar", "CheckCircle", "AlertCircle", "api", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CampaignWizard", "adAccounts", "selectedAccount", "onClose", "onSuccess", "_s", "currentStep", "setCurrentStep", "loading", "setLoading", "targetingOptions", "setTargetingOptions", "creativeTemplates", "setCreativeTemplates", "campaignObjectives", "setCampaignObjectives", "facebookPages", "setFacebookPages", "leadForms", "setLeadForms", "leadFormTemplates", "setLeadFormTemplates", "register", "handleSubmit", "watch", "setValue", "formState", "errors", "defaultValues", "campaign", "name", "objective", "status", "adSet", "dailyBudget", "targeting", "age_min", "age_max", "genders", "geo_locations", "countries", "optimizationGoal", "billingEvent", "bidStrategy", "ad", "creative", "object_story_spec", "page_id", "link_data", "link", "message", "description", "call_to_action", "type", "leadForm", "useExisting", "existingFormId", "newForm", "privacyPolicyUrl", "questions", "<PERSON><PERSON><PERSON><PERSON>", "loadFormData", "loadLeadForms", "targetingRes", "creativeRes", "objectivesRes", "pagesRes", "leadFormTemplatesRes", "Promise", "all", "get", "data", "recommended", "length", "id", "error", "console", "optimizationGoals", "billingEvents", "bidStrategies", "callToActionTypes", "response", "steps", "title", "icon", "nextStep", "prevStep", "onSubmit", "_data$campaign", "_data$campaign2", "isLeadGenCampaign", "payload", "adAccountId", "log", "post", "success", "leadFormId", "_error$response", "_error$response$data", "renderStepContent", "_errors$campaign", "_targetingOptions$opt", "_watchedValues$campai", "_watchedValues$campai2", "_watchedValues$leadFo", "_watchedValues$leadFo2", "_creativeTemplates$ca", "_watchedValues$campai3", "_watchedValues$campai4", "_watchedValues$adSet", "_watchedValues$adSet2", "_watchedValues$adSet3", "_watchedValues$adSet4", "_watchedValues$adSet5", "_watchedValues$adSet6", "_watchedValues$ad", "_watchedValues$ad2", "_watchedValues$ad2$cr", "_watchedValues$ad2$cr2", "_watchedValues$ad2$cr3", "_watchedValues$ad2$cr4", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "required", "placeholder", "map", "value", "replace", "min", "max", "step", "goal", "label", "page", "form", "leads_count", "pattern", "rows", "cta", "substring", "onClick", "index", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/FB API SCRAPER/pressure-max-frontend/src/components/CampaignWizard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useForm } from 'react-hook-form';\nimport toast from 'react-hot-toast';\nimport { \n  ChevronLeft, \n  ChevronRight, \n  Target, \n  Image, \n  DollarSign, \n  Calendar,\n  CheckCircle,\n  AlertCircle\n} from 'lucide-react';\nimport api from '../services/api';\n\nconst CampaignWizard = ({ adAccounts, selectedAccount, onClose, onSuccess }) => {\n  const [currentStep, setCurrentStep] = useState(1);\n  const [loading, setLoading] = useState(false);\n  const [targetingOptions, setTargetingOptions] = useState({});\n  const [creativeTemplates, setCreativeTemplates] = useState({});\n  const [campaignObjectives, setCampaignObjectives] = useState([]);\n  const [facebookPages, setFacebookPages] = useState([]);\n  const [leadForms, setLeadForms] = useState([]);\n  const [leadFormTemplates, setLeadFormTemplates] = useState({});\n  \n  const { register, handleSubmit, watch, setValue, formState: { errors } } = useForm({\n    defaultValues: {\n      campaign: {\n        name: '',\n        objective: 'OUTCOME_TRAFFIC',\n        status: 'PAUSED'\n      },\n      adSet: {\n        name: '',\n        dailyBudget: '10',\n        targeting: {\n          age_min: 18,\n          age_max: 65,\n          genders: [0],\n          geo_locations: {\n            countries: ['US']\n          }\n        },\n        optimizationGoal: 'LINK_CLICKS',\n        billingEvent: 'LINK_CLICKS',\n        bidStrategy: 'LOWEST_COST_WITHOUT_CAP',\n        status: 'PAUSED'\n      },\n      ad: {\n        name: '',\n        creative: {\n          object_story_spec: {\n            page_id: '',\n            link_data: {\n              link: '',\n              message: '',\n              name: '',\n              description: '',\n              call_to_action: {\n                type: 'LEARN_MORE'\n              }\n            }\n          }\n        },\n        status: 'PAUSED'\n      },\n      leadForm: {\n        useExisting: true,\n        existingFormId: '',\n        newForm: {\n          name: '',\n          privacyPolicyUrl: '',\n          questions: [\n            { type: 'FIRST_NAME' },\n            { type: 'LAST_NAME' },\n            { type: 'EMAIL' }\n          ]\n        }\n      }\n    }\n  });\n\n  const watchedValues = watch();\n\n  useEffect(() => {\n    loadFormData();\n  }, []);\n\n  useEffect(() => {\n    if (selectedAccount) {\n      loadLeadForms();\n    }\n  }, [selectedAccount]);\n\n  const loadFormData = async () => {\n    try {\n      const [targetingRes, creativeRes, objectivesRes, pagesRes, leadFormTemplatesRes] = await Promise.all([\n        api.get('/facebook/targeting-options'),\n        api.get('/facebook/creative-templates'),\n        api.get('/facebook/campaign-objectives'),\n        api.get('/facebook/pages'),\n        api.get('/facebook/leadform-templates')\n      ]);\n\n      setTargetingOptions(targetingRes.data);\n      setCreativeTemplates(creativeRes.data);\n      setCampaignObjectives(objectivesRes.data.recommended || []);\n      setFacebookPages(pagesRes.data || []);\n      setLeadFormTemplates(leadFormTemplatesRes.data || {});\n\n      // Auto-select first page if available\n      if (pagesRes.data && pagesRes.data.length > 0) {\n        setValue('ad.creative.object_story_spec.page_id', pagesRes.data[0].id);\n      }\n\n      // Load lead forms if we have an account selected\n      if (selectedAccount) {\n        loadLeadForms();\n      }\n    } catch (error) {\n      console.error('Error loading form data:', error);\n      toast.error('Failed to load form options');\n      // Set fallback empty data to prevent rendering issues\n      setTargetingOptions({\n        optimizationGoals: [],\n        billingEvents: [],\n        bidStrategies: []\n      });\n      setCreativeTemplates({\n        callToActionTypes: []\n      });\n      setCampaignObjectives(['OUTCOME_TRAFFIC', 'OUTCOME_ENGAGEMENT']);\n      setFacebookPages([{ id: 'demo_page_123', name: 'Demo Page' }]);\n    }\n  };\n\n  const loadLeadForms = async () => {\n    if (!selectedAccount) return;\n\n    try {\n      const response = await api.get(`/facebook/leadforms/${selectedAccount}`);\n      setLeadForms(response.data || []);\n    } catch (error) {\n      console.error('Error loading lead forms:', error);\n      setLeadForms([]);\n    }\n  };\n\n  const steps = [\n    { \n      id: 1, \n      title: 'Campaign Details', \n      icon: Target,\n      description: 'Set campaign name and objective'\n    },\n    { \n      id: 2, \n      title: 'Targeting & Budget', \n      icon: DollarSign,\n      description: 'Define audience and budget'\n    },\n    { \n      id: 3, \n      title: 'Creative & Ad', \n      icon: Image,\n      description: 'Create ad content and design'\n    },\n    { \n      id: 4, \n      title: 'Review & Launch', \n      icon: CheckCircle,\n      description: 'Review and create campaign'\n    }\n  ];\n\n  const nextStep = () => {\n    if (currentStep < steps.length) {\n      setCurrentStep(currentStep + 1);\n    }\n  };\n\n  const prevStep = () => {\n    if (currentStep > 1) {\n      setCurrentStep(currentStep - 1);\n    }\n  };\n\n  const onSubmit = async (data) => {\n    if (currentStep < steps.length) {\n      nextStep();\n      return;\n    }\n\n    // Final submission\n    setLoading(true);\n    try {\n      const isLeadGenCampaign = data.campaign?.objective === 'OUTCOME_LEADS' ||\n                                data.campaign?.objective === 'LEAD_GENERATION';\n\n      const payload = {\n        adAccountId: selectedAccount,\n        campaign: data.campaign,\n        adSet: data.adSet,\n        ad: data.ad\n      };\n\n      // Add lead form data if this is a lead generation campaign\n      if (isLeadGenCampaign && data.leadForm) {\n        payload.leadForm = data.leadForm;\n      }\n\n      console.log('Creating campaign hierarchy:', payload);\n\n      const response = await api.post('/facebook/campaign-hierarchy', payload);\n\n      if (response.data.isLeadGenCampaign) {\n        toast.success(`Lead generation campaign created successfully! ${response.data.leadFormId ? 'Lead form ID: ' + response.data.leadFormId : ''}`);\n      } else {\n        toast.success('Campaign hierarchy created successfully!');\n      }\n\n      onSuccess(response.data);\n      onClose();\n    } catch (error) {\n      console.error('Error creating campaign hierarchy:', error);\n      toast.error(error.response?.data?.error || 'Failed to create campaign hierarchy');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const renderStepContent = () => {\n    switch (currentStep) {\n      case 1:\n        return (\n          <div className=\"space-y-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900\">Campaign Details</h3>\n            \n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Campaign Name *\n              </label>\n              <input\n                {...register('campaign.name', { required: 'Campaign name is required' })}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                placeholder=\"Enter campaign name\"\n              />\n              {errors.campaign?.name && (\n                <p className=\"mt-1 text-sm text-red-600\">{errors.campaign.name.message}</p>\n              )}\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Campaign Objective *\n              </label>\n              <select\n                {...register('campaign.objective', { required: 'Objective is required' })}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              >\n                {campaignObjectives?.map(objective => (\n                  <option key={objective} value={objective}>\n                    {objective.replace('OUTCOME_', '').replace('_', ' ')}\n                  </option>\n                )) || []}\n              </select>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Campaign Status\n              </label>\n              <select\n                {...register('campaign.status')}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              >\n                <option key=\"PAUSED\" value=\"PAUSED\">Paused</option>\n                <option key=\"ACTIVE\" value=\"ACTIVE\">Active</option>\n              </select>\n            </div>\n          </div>\n        );\n\n      case 2:\n        return (\n          <div className=\"space-y-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900\">Targeting & Budget</h3>\n            \n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Ad Set Name *\n              </label>\n              <input\n                {...register('adSet.name', { required: 'Ad set name is required' })}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                placeholder=\"Enter ad set name\"\n              />\n            </div>\n\n            <div className=\"grid grid-cols-2 gap-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Age Min\n                </label>\n                <input\n                  {...register('adSet.targeting.age_min', { \n                    required: 'Age min is required',\n                    min: { value: 18, message: 'Minimum age is 18' }\n                  })}\n                  type=\"number\"\n                  min=\"18\"\n                  max=\"65\"\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Age Max\n                </label>\n                <input\n                  {...register('adSet.targeting.age_max', { \n                    required: 'Age max is required',\n                    max: { value: 65, message: 'Maximum age is 65' }\n                  })}\n                  type=\"number\"\n                  min=\"18\"\n                  max=\"65\"\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                />\n              </div>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Daily Budget (USD) *\n              </label>\n              <input\n                {...register('adSet.dailyBudget', { \n                  required: 'Daily budget is required',\n                  min: { value: 1, message: 'Minimum budget is $1' }\n                })}\n                type=\"number\"\n                min=\"1\"\n                step=\"0.01\"\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                placeholder=\"10.00\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Optimization Goal\n              </label>\n              <select\n                {...register('adSet.optimizationGoal')}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              >\n                {targetingOptions?.optimizationGoals?.map(goal => (\n                  <option key={goal.value} value={goal.value}>\n                    {goal.label}\n                  </option>\n                )) || []}\n              </select>\n            </div>\n          </div>\n        );\n\n      case 3:\n        const isLeadGenCampaign = watchedValues.campaign?.objective === 'OUTCOME_LEADS' ||\n                                  watchedValues.campaign?.objective === 'LEAD_GENERATION';\n\n        return (\n          <div className=\"space-y-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900\">Creative & Ad</h3>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Ad Name *\n              </label>\n              <input\n                {...register('ad.name', { required: 'Ad name is required' })}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                placeholder=\"Enter ad name\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Facebook Page *\n              </label>\n              <select\n                {...register('ad.creative.object_story_spec.page_id', { required: 'Facebook page is required' })}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              >\n                {facebookPages?.map(page => (\n                  <option key={page.id} value={page.id}>\n                    {page.name}\n                  </option>\n                )) || []}\n              </select>\n            </div>\n\n            {isLeadGenCampaign ? (\n              // Lead Generation Creative\n              <div className=\"space-y-4\">\n                <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n                  <h4 className=\"text-sm font-medium text-blue-800 mb-2\">Lead Generation Campaign</h4>\n                  <p className=\"text-sm text-blue-700\">\n                    This campaign will use lead forms to collect user information directly on Facebook.\n                  </p>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-3\">\n                    Lead Form Setup\n                  </label>\n                  <div className=\"space-y-3\">\n                    <label className=\"flex items-center\">\n                      <input\n                        type=\"radio\"\n                        {...register('leadForm.useExisting')}\n                        value={true}\n                        className=\"mr-2\"\n                      />\n                      <span className=\"text-sm text-gray-700\">Use existing lead form</span>\n                    </label>\n                    <label className=\"flex items-center\">\n                      <input\n                        type=\"radio\"\n                        {...register('leadForm.useExisting')}\n                        value={false}\n                        className=\"mr-2\"\n                      />\n                      <span className=\"text-sm text-gray-700\">Create new lead form</span>\n                    </label>\n                  </div>\n                </div>\n\n                {watchedValues.leadForm?.useExisting === 'true' || watchedValues.leadForm?.useExisting === true ? (\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Select Lead Form *\n                    </label>\n                    <select\n                      {...register('leadForm.existingFormId', { required: 'Lead form is required' })}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    >\n                      <option value=\"\">Select a lead form</option>\n                      {leadForms?.map(form => (\n                        <option key={form.id} value={form.id}>\n                          {form.name} ({form.leads_count || 0} leads)\n                        </option>\n                      )) || []}\n                    </select>\n                    {leadForms?.length === 0 && (\n                      <p className=\"mt-1 text-sm text-yellow-600\">\n                        No lead forms found. Create one first or choose \"Create new lead form\" option.\n                      </p>\n                    )}\n                  </div>\n                ) : (\n                  <div className=\"space-y-4\">\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        Lead Form Name *\n                      </label>\n                      <input\n                        {...register('leadForm.newForm.name', { required: 'Lead form name is required' })}\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                        placeholder=\"Enter lead form name\"\n                      />\n                    </div>\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        Privacy Policy URL *\n                      </label>\n                      <input\n                        {...register('leadForm.newForm.privacyPolicyUrl', {\n                          required: 'Privacy policy URL is required',\n                          pattern: {\n                            value: /^https?:\\/\\/.+/,\n                            message: 'Please enter a valid URL'\n                          }\n                        })}\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                        placeholder=\"https://example.com/privacy-policy\"\n                      />\n                    </div>\n                  </div>\n                )}\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Primary Text *\n                  </label>\n                  <textarea\n                    {...register('ad.creative.object_story_spec.link_data.message', {\n                      required: 'Primary text is required'\n                    })}\n                    rows=\"3\"\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    placeholder=\"Write compelling ad copy that encourages users to fill out your lead form...\"\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Headline *\n                  </label>\n                  <input\n                    {...register('ad.creative.object_story_spec.link_data.name', {\n                      required: 'Headline is required'\n                    })}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    placeholder=\"Enter compelling headline\"\n                  />\n                </div>\n              </div>\n            ) : (\n              // Regular Link Creative\n              <div className=\"space-y-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Destination URL *\n                  </label>\n                  <input\n                    {...register('ad.creative.object_story_spec.link_data.link', {\n                      required: 'Destination URL is required',\n                      pattern: {\n                        value: /^https?:\\/\\/.+/,\n                        message: 'Please enter a valid URL'\n                      }\n                    })}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    placeholder=\"https://example.com\"\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Primary Text *\n                  </label>\n                  <textarea\n                    {...register('ad.creative.object_story_spec.link_data.message', {\n                      required: 'Primary text is required'\n                    })}\n                    rows=\"3\"\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    placeholder=\"Write compelling ad copy...\"\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Headline *\n                  </label>\n                  <input\n                    {...register('ad.creative.object_story_spec.link_data.name', {\n                      required: 'Headline is required'\n                    })}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    placeholder=\"Enter headline\"\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Description\n                  </label>\n                  <input\n                    {...register('ad.creative.object_story_spec.link_data.description')}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    placeholder=\"Enter description\"\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Call to Action\n                  </label>\n                  <select\n                    {...register('ad.creative.object_story_spec.link_data.call_to_action.type')}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  >\n                    {creativeTemplates?.callToActionTypes?.map(cta => (\n                      <option key={cta.value} value={cta.value}>\n                        {cta.label}\n                      </option>\n                    )) || []}\n                  </select>\n                </div>\n              </div>\n            )}\n          </div>\n        );\n\n      case 4:\n        return (\n          <div className=\"space-y-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900\">Review & Launch</h3>\n            \n            <div className=\"bg-gray-50 p-4 rounded-lg space-y-4\">\n              <div>\n                <h4 className=\"font-medium text-gray-900\">Campaign</h4>\n                <p className=\"text-sm text-gray-600\">\n                  {watchedValues.campaign?.name} - {watchedValues.campaign?.objective}\n                </p>\n              </div>\n              \n              <div>\n                <h4 className=\"font-medium text-gray-900\">Ad Set</h4>\n                <p className=\"text-sm text-gray-600\">\n                  {watchedValues.adSet?.name} - ${watchedValues.adSet?.dailyBudget}/day\n                </p>\n                <p className=\"text-sm text-gray-600\">\n                  Ages {watchedValues.adSet?.targeting?.age_min}-{watchedValues.adSet?.targeting?.age_max}\n                </p>\n              </div>\n              \n              <div>\n                <h4 className=\"font-medium text-gray-900\">Ad</h4>\n                <p className=\"text-sm text-gray-600\">\n                  {watchedValues.ad?.name}\n                </p>\n                <p className=\"text-sm text-gray-600\">\n                  {watchedValues.ad?.creative?.object_story_spec?.link_data?.message?.substring(0, 100)}...\n                </p>\n              </div>\n            </div>\n\n            <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-4\">\n              <div className=\"flex\">\n                <AlertCircle className=\"h-5 w-5 text-yellow-400 mr-2 mt-0.5\" />\n                <div>\n                  <h4 className=\"text-sm font-medium text-yellow-800\">Ready to Create</h4>\n                  <p className=\"text-sm text-yellow-700\">\n                    This will create a complete campaign hierarchy with campaign, ad set, and ad.\n                    All entities will be created in PAUSED status for your review.\n                  </p>\n                </div>\n              </div>\n            </div>\n          </div>\n        );\n\n      default:\n        return null;\n    }\n  };\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n      <div className=\"bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden\">\n        {/* Header */}\n        <div className=\"px-6 py-4 border-b border-gray-200\">\n          <div className=\"flex items-center justify-between\">\n            <h2 className=\"text-xl font-semibold text-gray-900\">\n              Create Campaign Hierarchy\n            </h2>\n            <button\n              onClick={onClose}\n              className=\"text-gray-400 hover:text-gray-600\"\n            >\n              ×\n            </button>\n          </div>\n          \n          {/* Progress Steps */}\n          <div className=\"mt-4\">\n            <div className=\"flex items-center justify-between\">\n              {steps.map((step, index) => (\n                <div key={step.id} className=\"flex items-center\">\n                  <div className={`flex items-center justify-center w-8 h-8 rounded-full ${\n                    currentStep >= step.id \n                      ? 'bg-blue-600 text-white' \n                      : 'bg-gray-200 text-gray-600'\n                  }`}>\n                    {currentStep > step.id ? (\n                      <CheckCircle className=\"h-5 w-5\" />\n                    ) : (\n                      <step.icon className=\"h-4 w-4\" />\n                    )}\n                  </div>\n                  <div className=\"ml-2 hidden sm:block\">\n                    <p className={`text-sm font-medium ${\n                      currentStep >= step.id ? 'text-blue-600' : 'text-gray-500'\n                    }`}>\n                      {step.title}\n                    </p>\n                  </div>\n                  {index < steps.length - 1 && (\n                    <div className={`w-12 h-0.5 mx-4 ${\n                      currentStep > step.id ? 'bg-blue-600' : 'bg-gray-200'\n                    }`} />\n                  )}\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n\n        {/* Content */}\n        <form onSubmit={handleSubmit(onSubmit)} className=\"flex flex-col h-full\">\n          <div className=\"flex-1 px-6 py-6 overflow-y-auto\">\n            {renderStepContent()}\n          </div>\n\n          {/* Footer */}\n          <div className=\"px-6 py-4 border-t border-gray-200 flex justify-between\">\n            <button\n              type=\"button\"\n              onClick={prevStep}\n              disabled={currentStep === 1}\n              className=\"flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              <ChevronLeft className=\"h-4 w-4 mr-1\" />\n              Previous\n            </button>\n\n            <button\n              type=\"submit\"\n              disabled={loading}\n              className=\"flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {loading ? (\n                'Creating...'\n              ) : currentStep === steps.length ? (\n                'Create Campaign Hierarchy'\n              ) : (\n                <>\n                  Next\n                  <ChevronRight className=\"h-4 w-4 ml-1\" />\n                </>\n              )}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n};\n\nexport default CampaignWizard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,iBAAiB;AACzC,OAAOC,KAAK,MAAM,iBAAiB;AACnC,SACEC,WAAW,EACXC,YAAY,EACZC,MAAM,EACNC,KAAK,EACLC,UAAU,EACVC,QAAQ,EACRC,WAAW,EACXC,WAAW,QACN,cAAc;AACrB,OAAOC,GAAG,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAElC,MAAMC,cAAc,GAAGA,CAAC;EAAEC,UAAU;EAAEC,eAAe;EAAEC,OAAO;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EAC9E,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACyB,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC2B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5B,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5D,MAAM,CAAC6B,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG9B,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC9D,MAAM,CAAC+B,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAACiC,aAAa,EAAEC,gBAAgB,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACmC,SAAS,EAAEC,YAAY,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACqC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGtC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAE9D,MAAM;IAAEuC,QAAQ;IAAEC,YAAY;IAAEC,KAAK;IAAEC,QAAQ;IAAEC,SAAS,EAAE;MAAEC;IAAO;EAAE,CAAC,GAAG1C,OAAO,CAAC;IACjF2C,aAAa,EAAE;MACbC,QAAQ,EAAE;QACRC,IAAI,EAAE,EAAE;QACRC,SAAS,EAAE,iBAAiB;QAC5BC,MAAM,EAAE;MACV,CAAC;MACDC,KAAK,EAAE;QACLH,IAAI,EAAE,EAAE;QACRI,WAAW,EAAE,IAAI;QACjBC,SAAS,EAAE;UACTC,OAAO,EAAE,EAAE;UACXC,OAAO,EAAE,EAAE;UACXC,OAAO,EAAE,CAAC,CAAC,CAAC;UACZC,aAAa,EAAE;YACbC,SAAS,EAAE,CAAC,IAAI;UAClB;QACF,CAAC;QACDC,gBAAgB,EAAE,aAAa;QAC/BC,YAAY,EAAE,aAAa;QAC3BC,WAAW,EAAE,yBAAyB;QACtCX,MAAM,EAAE;MACV,CAAC;MACDY,EAAE,EAAE;QACFd,IAAI,EAAE,EAAE;QACRe,QAAQ,EAAE;UACRC,iBAAiB,EAAE;YACjBC,OAAO,EAAE,EAAE;YACXC,SAAS,EAAE;cACTC,IAAI,EAAE,EAAE;cACRC,OAAO,EAAE,EAAE;cACXpB,IAAI,EAAE,EAAE;cACRqB,WAAW,EAAE,EAAE;cACfC,cAAc,EAAE;gBACdC,IAAI,EAAE;cACR;YACF;UACF;QACF,CAAC;QACDrB,MAAM,EAAE;MACV,CAAC;MACDsB,QAAQ,EAAE;QACRC,WAAW,EAAE,IAAI;QACjBC,cAAc,EAAE,EAAE;QAClBC,OAAO,EAAE;UACP3B,IAAI,EAAE,EAAE;UACR4B,gBAAgB,EAAE,EAAE;UACpBC,SAAS,EAAE,CACT;YAAEN,IAAI,EAAE;UAAa,CAAC,EACtB;YAAEA,IAAI,EAAE;UAAY,CAAC,EACrB;YAAEA,IAAI,EAAE;UAAQ,CAAC;QAErB;MACF;IACF;EACF,CAAC,CAAC;EAEF,MAAMO,aAAa,GAAGpC,KAAK,CAAC,CAAC;EAE7BxC,SAAS,CAAC,MAAM;IACd6E,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EAEN7E,SAAS,CAAC,MAAM;IACd,IAAIkB,eAAe,EAAE;MACnB4D,aAAa,CAAC,CAAC;IACjB;EACF,CAAC,EAAE,CAAC5D,eAAe,CAAC,CAAC;EAErB,MAAM2D,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAM,CAACE,YAAY,EAAEC,WAAW,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,oBAAoB,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACnG1E,GAAG,CAAC2E,GAAG,CAAC,6BAA6B,CAAC,EACtC3E,GAAG,CAAC2E,GAAG,CAAC,8BAA8B,CAAC,EACvC3E,GAAG,CAAC2E,GAAG,CAAC,+BAA+B,CAAC,EACxC3E,GAAG,CAAC2E,GAAG,CAAC,iBAAiB,CAAC,EAC1B3E,GAAG,CAAC2E,GAAG,CAAC,8BAA8B,CAAC,CACxC,CAAC;MAEF3D,mBAAmB,CAACoD,YAAY,CAACQ,IAAI,CAAC;MACtC1D,oBAAoB,CAACmD,WAAW,CAACO,IAAI,CAAC;MACtCxD,qBAAqB,CAACkD,aAAa,CAACM,IAAI,CAACC,WAAW,IAAI,EAAE,CAAC;MAC3DvD,gBAAgB,CAACiD,QAAQ,CAACK,IAAI,IAAI,EAAE,CAAC;MACrClD,oBAAoB,CAAC8C,oBAAoB,CAACI,IAAI,IAAI,CAAC,CAAC,CAAC;;MAErD;MACA,IAAIL,QAAQ,CAACK,IAAI,IAAIL,QAAQ,CAACK,IAAI,CAACE,MAAM,GAAG,CAAC,EAAE;QAC7ChD,QAAQ,CAAC,uCAAuC,EAAEyC,QAAQ,CAACK,IAAI,CAAC,CAAC,CAAC,CAACG,EAAE,CAAC;MACxE;;MAEA;MACA,IAAIxE,eAAe,EAAE;QACnB4D,aAAa,CAAC,CAAC;MACjB;IACF,CAAC,CAAC,OAAOa,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDzF,KAAK,CAACyF,KAAK,CAAC,6BAA6B,CAAC;MAC1C;MACAhE,mBAAmB,CAAC;QAClBkE,iBAAiB,EAAE,EAAE;QACrBC,aAAa,EAAE,EAAE;QACjBC,aAAa,EAAE;MACjB,CAAC,CAAC;MACFlE,oBAAoB,CAAC;QACnBmE,iBAAiB,EAAE;MACrB,CAAC,CAAC;MACFjE,qBAAqB,CAAC,CAAC,iBAAiB,EAAE,oBAAoB,CAAC,CAAC;MAChEE,gBAAgB,CAAC,CAAC;QAAEyD,EAAE,EAAE,eAAe;QAAE5C,IAAI,EAAE;MAAY,CAAC,CAAC,CAAC;IAChE;EACF,CAAC;EAED,MAAMgC,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI,CAAC5D,eAAe,EAAE;IAEtB,IAAI;MACF,MAAM+E,QAAQ,GAAG,MAAMtF,GAAG,CAAC2E,GAAG,CAAC,uBAAuBpE,eAAe,EAAE,CAAC;MACxEiB,YAAY,CAAC8D,QAAQ,CAACV,IAAI,IAAI,EAAE,CAAC;IACnC,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDxD,YAAY,CAAC,EAAE,CAAC;IAClB;EACF,CAAC;EAED,MAAM+D,KAAK,GAAG,CACZ;IACER,EAAE,EAAE,CAAC;IACLS,KAAK,EAAE,kBAAkB;IACzBC,IAAI,EAAE/F,MAAM;IACZ8D,WAAW,EAAE;EACf,CAAC,EACD;IACEuB,EAAE,EAAE,CAAC;IACLS,KAAK,EAAE,oBAAoB;IAC3BC,IAAI,EAAE7F,UAAU;IAChB4D,WAAW,EAAE;EACf,CAAC,EACD;IACEuB,EAAE,EAAE,CAAC;IACLS,KAAK,EAAE,eAAe;IACtBC,IAAI,EAAE9F,KAAK;IACX6D,WAAW,EAAE;EACf,CAAC,EACD;IACEuB,EAAE,EAAE,CAAC;IACLS,KAAK,EAAE,iBAAiB;IACxBC,IAAI,EAAE3F,WAAW;IACjB0D,WAAW,EAAE;EACf,CAAC,CACF;EAED,MAAMkC,QAAQ,GAAGA,CAAA,KAAM;IACrB,IAAI/E,WAAW,GAAG4E,KAAK,CAACT,MAAM,EAAE;MAC9BlE,cAAc,CAACD,WAAW,GAAG,CAAC,CAAC;IACjC;EACF,CAAC;EAED,MAAMgF,QAAQ,GAAGA,CAAA,KAAM;IACrB,IAAIhF,WAAW,GAAG,CAAC,EAAE;MACnBC,cAAc,CAACD,WAAW,GAAG,CAAC,CAAC;IACjC;EACF,CAAC;EAED,MAAMiF,QAAQ,GAAG,MAAOhB,IAAI,IAAK;IAC/B,IAAIjE,WAAW,GAAG4E,KAAK,CAACT,MAAM,EAAE;MAC9BY,QAAQ,CAAC,CAAC;MACV;IACF;;IAEA;IACA5E,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MAAA,IAAA+E,cAAA,EAAAC,eAAA;MACF,MAAMC,iBAAiB,GAAG,EAAAF,cAAA,GAAAjB,IAAI,CAAC1C,QAAQ,cAAA2D,cAAA,uBAAbA,cAAA,CAAezD,SAAS,MAAK,eAAe,IAC5C,EAAA0D,eAAA,GAAAlB,IAAI,CAAC1C,QAAQ,cAAA4D,eAAA,uBAAbA,eAAA,CAAe1D,SAAS,MAAK,iBAAiB;MAExE,MAAM4D,OAAO,GAAG;QACdC,WAAW,EAAE1F,eAAe;QAC5B2B,QAAQ,EAAE0C,IAAI,CAAC1C,QAAQ;QACvBI,KAAK,EAAEsC,IAAI,CAACtC,KAAK;QACjBW,EAAE,EAAE2B,IAAI,CAAC3B;MACX,CAAC;;MAED;MACA,IAAI8C,iBAAiB,IAAInB,IAAI,CAACjB,QAAQ,EAAE;QACtCqC,OAAO,CAACrC,QAAQ,GAAGiB,IAAI,CAACjB,QAAQ;MAClC;MAEAsB,OAAO,CAACiB,GAAG,CAAC,8BAA8B,EAAEF,OAAO,CAAC;MAEpD,MAAMV,QAAQ,GAAG,MAAMtF,GAAG,CAACmG,IAAI,CAAC,8BAA8B,EAAEH,OAAO,CAAC;MAExE,IAAIV,QAAQ,CAACV,IAAI,CAACmB,iBAAiB,EAAE;QACnCxG,KAAK,CAAC6G,OAAO,CAAC,kDAAkDd,QAAQ,CAACV,IAAI,CAACyB,UAAU,GAAG,gBAAgB,GAAGf,QAAQ,CAACV,IAAI,CAACyB,UAAU,GAAG,EAAE,EAAE,CAAC;MAChJ,CAAC,MAAM;QACL9G,KAAK,CAAC6G,OAAO,CAAC,0CAA0C,CAAC;MAC3D;MAEA3F,SAAS,CAAC6E,QAAQ,CAACV,IAAI,CAAC;MACxBpE,OAAO,CAAC,CAAC;IACX,CAAC,CAAC,OAAOwE,KAAK,EAAE;MAAA,IAAAsB,eAAA,EAAAC,oBAAA;MACdtB,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1DzF,KAAK,CAACyF,KAAK,CAAC,EAAAsB,eAAA,GAAAtB,KAAK,CAACM,QAAQ,cAAAgB,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgB1B,IAAI,cAAA2B,oBAAA,uBAApBA,oBAAA,CAAsBvB,KAAK,KAAI,qCAAqC,CAAC;IACnF,CAAC,SAAS;MACRlE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM0F,iBAAiB,GAAGA,CAAA,KAAM;IAAA,IAAAC,gBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,iBAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;IAC9B,QAAQlH,WAAW;MACjB,KAAK,CAAC;QACJ,oBACET,OAAA;UAAK4H,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB7H,OAAA;YAAI4H,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAEzEjI,OAAA;YAAA6H,QAAA,gBACE7H,OAAA;cAAO4H,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRjI,OAAA;cAAA,GACMyB,QAAQ,CAAC,eAAe,EAAE;gBAAEyG,QAAQ,EAAE;cAA4B,CAAC,CAAC;cACxEN,SAAS,EAAC,wGAAwG;cAClHO,WAAW,EAAC;YAAqB;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,EACD,EAAA1B,gBAAA,GAAAzE,MAAM,CAACE,QAAQ,cAAAuE,gBAAA,uBAAfA,gBAAA,CAAiBtE,IAAI,kBACpBjC,OAAA;cAAG4H,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAE/F,MAAM,CAACE,QAAQ,CAACC,IAAI,CAACoB;YAAO;cAAAyE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAC3E;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENjI,OAAA;YAAA6H,QAAA,gBACE7H,OAAA;cAAO4H,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRjI,OAAA;cAAA,GACMyB,QAAQ,CAAC,oBAAoB,EAAE;gBAAEyG,QAAQ,EAAE;cAAwB,CAAC,CAAC;cACzEN,SAAS,EAAC,wGAAwG;cAAAC,QAAA,EAEjH,CAAA5G,kBAAkB,aAAlBA,kBAAkB,uBAAlBA,kBAAkB,CAAEmH,GAAG,CAAClG,SAAS,iBAChClC,OAAA;gBAAwBqI,KAAK,EAAEnG,SAAU;gBAAA2F,QAAA,EACtC3F,SAAS,CAACoG,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,GAAG,EAAE,GAAG;cAAC,GADzCpG,SAAS;gBAAA4F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEd,CACT,CAAC,KAAI;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENjI,OAAA;YAAA6H,QAAA,gBACE7H,OAAA;cAAO4H,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRjI,OAAA;cAAA,GACMyB,QAAQ,CAAC,iBAAiB,CAAC;cAC/BmG,SAAS,EAAC,wGAAwG;cAAAC,QAAA,gBAElH7H,OAAA;gBAAqBqI,KAAK,EAAC,QAAQ;gBAAAR,QAAA,EAAC;cAAM,GAA9B,QAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAA8B,CAAC,eACnDjI,OAAA;gBAAqBqI,KAAK,EAAC,QAAQ;gBAAAR,QAAA,EAAC;cAAM,GAA9B,QAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAA8B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAGV,KAAK,CAAC;QACJ,oBACEjI,OAAA;UAAK4H,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB7H,OAAA;YAAI4H,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAE3EjI,OAAA;YAAA6H,QAAA,gBACE7H,OAAA;cAAO4H,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRjI,OAAA;cAAA,GACMyB,QAAQ,CAAC,YAAY,EAAE;gBAAEyG,QAAQ,EAAE;cAA0B,CAAC,CAAC;cACnEN,SAAS,EAAC,wGAAwG;cAClHO,WAAW,EAAC;YAAmB;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENjI,OAAA;YAAK4H,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACrC7H,OAAA;cAAA6H,QAAA,gBACE7H,OAAA;gBAAO4H,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRjI,OAAA;gBAAA,GACMyB,QAAQ,CAAC,yBAAyB,EAAE;kBACtCyG,QAAQ,EAAE,qBAAqB;kBAC/BK,GAAG,EAAE;oBAAEF,KAAK,EAAE,EAAE;oBAAEhF,OAAO,EAAE;kBAAoB;gBACjD,CAAC,CAAC;gBACFG,IAAI,EAAC,QAAQ;gBACb+E,GAAG,EAAC,IAAI;gBACRC,GAAG,EAAC,IAAI;gBACRZ,SAAS,EAAC;cAAwG;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNjI,OAAA;cAAA6H,QAAA,gBACE7H,OAAA;gBAAO4H,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRjI,OAAA;gBAAA,GACMyB,QAAQ,CAAC,yBAAyB,EAAE;kBACtCyG,QAAQ,EAAE,qBAAqB;kBAC/BM,GAAG,EAAE;oBAAEH,KAAK,EAAE,EAAE;oBAAEhF,OAAO,EAAE;kBAAoB;gBACjD,CAAC,CAAC;gBACFG,IAAI,EAAC,QAAQ;gBACb+E,GAAG,EAAC,IAAI;gBACRC,GAAG,EAAC,IAAI;gBACRZ,SAAS,EAAC;cAAwG;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENjI,OAAA;YAAA6H,QAAA,gBACE7H,OAAA;cAAO4H,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRjI,OAAA;cAAA,GACMyB,QAAQ,CAAC,mBAAmB,EAAE;gBAChCyG,QAAQ,EAAE,0BAA0B;gBACpCK,GAAG,EAAE;kBAAEF,KAAK,EAAE,CAAC;kBAAEhF,OAAO,EAAE;gBAAuB;cACnD,CAAC,CAAC;cACFG,IAAI,EAAC,QAAQ;cACb+E,GAAG,EAAC,GAAG;cACPE,IAAI,EAAC,MAAM;cACXb,SAAS,EAAC,wGAAwG;cAClHO,WAAW,EAAC;YAAO;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENjI,OAAA;YAAA6H,QAAA,gBACE7H,OAAA;cAAO4H,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRjI,OAAA;cAAA,GACMyB,QAAQ,CAAC,wBAAwB,CAAC;cACtCmG,SAAS,EAAC,wGAAwG;cAAAC,QAAA,EAEjH,CAAAhH,gBAAgB,aAAhBA,gBAAgB,wBAAA2F,qBAAA,GAAhB3F,gBAAgB,CAAEmE,iBAAiB,cAAAwB,qBAAA,uBAAnCA,qBAAA,CAAqC4B,GAAG,CAACM,IAAI,iBAC5C1I,OAAA;gBAAyBqI,KAAK,EAAEK,IAAI,CAACL,KAAM;gBAAAR,QAAA,EACxCa,IAAI,CAACC;cAAK,GADAD,IAAI,CAACL,KAAK;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEf,CACT,CAAC,KAAI;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAGV,KAAK,CAAC;QACJ,MAAMpC,iBAAiB,GAAG,EAAAY,qBAAA,GAAA1C,aAAa,CAAC/B,QAAQ,cAAAyE,qBAAA,uBAAtBA,qBAAA,CAAwBvE,SAAS,MAAK,eAAe,IACrD,EAAAwE,sBAAA,GAAA3C,aAAa,CAAC/B,QAAQ,cAAA0E,sBAAA,uBAAtBA,sBAAA,CAAwBxE,SAAS,MAAK,iBAAiB;QAEjF,oBACElC,OAAA;UAAK4H,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB7H,OAAA;YAAI4H,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAEtEjI,OAAA;YAAA6H,QAAA,gBACE7H,OAAA;cAAO4H,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRjI,OAAA;cAAA,GACMyB,QAAQ,CAAC,SAAS,EAAE;gBAAEyG,QAAQ,EAAE;cAAsB,CAAC,CAAC;cAC5DN,SAAS,EAAC,wGAAwG;cAClHO,WAAW,EAAC;YAAe;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENjI,OAAA;YAAA6H,QAAA,gBACE7H,OAAA;cAAO4H,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEhE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRjI,OAAA;cAAA,GACMyB,QAAQ,CAAC,uCAAuC,EAAE;gBAAEyG,QAAQ,EAAE;cAA4B,CAAC,CAAC;cAChGN,SAAS,EAAC,wGAAwG;cAAAC,QAAA,EAEjH,CAAA1G,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEiH,GAAG,CAACQ,IAAI,iBACtB5I,OAAA;gBAAsBqI,KAAK,EAAEO,IAAI,CAAC/D,EAAG;gBAAAgD,QAAA,EAClCe,IAAI,CAAC3G;cAAI,GADC2G,IAAI,CAAC/D,EAAE;gBAAAiD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEZ,CACT,CAAC,KAAI;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EAELpC,iBAAiB;UAAA;UAChB;UACA7F,OAAA;YAAK4H,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB7H,OAAA;cAAK4H,SAAS,EAAC,kDAAkD;cAAAC,QAAA,gBAC/D7H,OAAA;gBAAI4H,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,EAAC;cAAwB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpFjI,OAAA;gBAAG4H,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAErC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAENjI,OAAA;cAAA6H,QAAA,gBACE7H,OAAA;gBAAO4H,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRjI,OAAA;gBAAK4H,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxB7H,OAAA;kBAAO4H,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAClC7H,OAAA;oBACEwD,IAAI,EAAC,OAAO;oBAAA,GACR/B,QAAQ,CAAC,sBAAsB,CAAC;oBACpC4G,KAAK,EAAE,IAAK;oBACZT,SAAS,EAAC;kBAAM;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB,CAAC,eACFjI,OAAA;oBAAM4H,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAAsB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChE,CAAC,eACRjI,OAAA;kBAAO4H,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAClC7H,OAAA;oBACEwD,IAAI,EAAC,OAAO;oBAAA,GACR/B,QAAQ,CAAC,sBAAsB,CAAC;oBACpC4G,KAAK,EAAE,KAAM;oBACbT,SAAS,EAAC;kBAAM;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB,CAAC,eACFjI,OAAA;oBAAM4H,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAAoB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAEL,EAAAtB,qBAAA,GAAA5C,aAAa,CAACN,QAAQ,cAAAkD,qBAAA,uBAAtBA,qBAAA,CAAwBjD,WAAW,MAAK,MAAM,IAAI,EAAAkD,sBAAA,GAAA7C,aAAa,CAACN,QAAQ,cAAAmD,sBAAA,uBAAtBA,sBAAA,CAAwBlD,WAAW,MAAK,IAAI,gBAC7F1D,OAAA;cAAA6H,QAAA,gBACE7H,OAAA;gBAAO4H,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRjI,OAAA;gBAAA,GACMyB,QAAQ,CAAC,yBAAyB,EAAE;kBAAEyG,QAAQ,EAAE;gBAAwB,CAAC,CAAC;gBAC9EN,SAAS,EAAC,wGAAwG;gBAAAC,QAAA,gBAElH7H,OAAA;kBAAQqI,KAAK,EAAC,EAAE;kBAAAR,QAAA,EAAC;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EAC3C,CAAA5G,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAE+G,GAAG,CAACS,IAAI,iBAClB7I,OAAA;kBAAsBqI,KAAK,EAAEQ,IAAI,CAAChE,EAAG;kBAAAgD,QAAA,GAClCgB,IAAI,CAAC5G,IAAI,EAAC,IAAE,EAAC4G,IAAI,CAACC,WAAW,IAAI,CAAC,EAAC,SACtC;gBAAA,GAFaD,IAAI,CAAChE,EAAE;kBAAAiD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEZ,CACT,CAAC,KAAI,EAAE;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,EACR,CAAA5G,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEuD,MAAM,MAAK,CAAC,iBACtB5E,OAAA;gBAAG4H,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,EAAC;cAE5C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,gBAENjI,OAAA;cAAK4H,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB7H,OAAA;gBAAA6H,QAAA,gBACE7H,OAAA;kBAAO4H,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRjI,OAAA;kBAAA,GACMyB,QAAQ,CAAC,uBAAuB,EAAE;oBAAEyG,QAAQ,EAAE;kBAA6B,CAAC,CAAC;kBACjFN,SAAS,EAAC,wGAAwG;kBAClHO,WAAW,EAAC;gBAAsB;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNjI,OAAA;gBAAA6H,QAAA,gBACE7H,OAAA;kBAAO4H,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRjI,OAAA;kBAAA,GACMyB,QAAQ,CAAC,mCAAmC,EAAE;oBAChDyG,QAAQ,EAAE,gCAAgC;oBAC1Ca,OAAO,EAAE;sBACPV,KAAK,EAAE,gBAAgB;sBACvBhF,OAAO,EAAE;oBACX;kBACF,CAAC,CAAC;kBACFuE,SAAS,EAAC,wGAAwG;kBAClHO,WAAW,EAAC;gBAAoC;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN,eAEDjI,OAAA;cAAA6H,QAAA,gBACE7H,OAAA;gBAAO4H,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRjI,OAAA;gBAAA,GACMyB,QAAQ,CAAC,iDAAiD,EAAE;kBAC9DyG,QAAQ,EAAE;gBACZ,CAAC,CAAC;gBACFc,IAAI,EAAC,GAAG;gBACRpB,SAAS,EAAC,wGAAwG;gBAClHO,WAAW,EAAC;cAA8E;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3F,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENjI,OAAA;cAAA6H,QAAA,gBACE7H,OAAA;gBAAO4H,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRjI,OAAA;gBAAA,GACMyB,QAAQ,CAAC,8CAA8C,EAAE;kBAC3DyG,QAAQ,EAAE;gBACZ,CAAC,CAAC;gBACFN,SAAS,EAAC,wGAAwG;gBAClHO,WAAW,EAAC;cAA2B;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;UAAA;UAEN;UACAjI,OAAA;YAAK4H,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB7H,OAAA;cAAA6H,QAAA,gBACE7H,OAAA;gBAAO4H,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRjI,OAAA;gBAAA,GACMyB,QAAQ,CAAC,8CAA8C,EAAE;kBAC3DyG,QAAQ,EAAE,6BAA6B;kBACvCa,OAAO,EAAE;oBACPV,KAAK,EAAE,gBAAgB;oBACvBhF,OAAO,EAAE;kBACX;gBACF,CAAC,CAAC;gBACFuE,SAAS,EAAC,wGAAwG;gBAClHO,WAAW,EAAC;cAAqB;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENjI,OAAA;cAAA6H,QAAA,gBACE7H,OAAA;gBAAO4H,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRjI,OAAA;gBAAA,GACMyB,QAAQ,CAAC,iDAAiD,EAAE;kBAC9DyG,QAAQ,EAAE;gBACZ,CAAC,CAAC;gBACFc,IAAI,EAAC,GAAG;gBACRpB,SAAS,EAAC,wGAAwG;gBAClHO,WAAW,EAAC;cAA6B;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENjI,OAAA;cAAA6H,QAAA,gBACE7H,OAAA;gBAAO4H,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRjI,OAAA;gBAAA,GACMyB,QAAQ,CAAC,8CAA8C,EAAE;kBAC3DyG,QAAQ,EAAE;gBACZ,CAAC,CAAC;gBACFN,SAAS,EAAC,wGAAwG;gBAClHO,WAAW,EAAC;cAAgB;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENjI,OAAA;cAAA6H,QAAA,gBACE7H,OAAA;gBAAO4H,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRjI,OAAA;gBAAA,GACMyB,QAAQ,CAAC,qDAAqD,CAAC;gBACnEmG,SAAS,EAAC,wGAAwG;gBAClHO,WAAW,EAAC;cAAmB;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENjI,OAAA;cAAA6H,QAAA,gBACE7H,OAAA;gBAAO4H,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRjI,OAAA;gBAAA,GACMyB,QAAQ,CAAC,6DAA6D,CAAC;gBAC3EmG,SAAS,EAAC,wGAAwG;gBAAAC,QAAA,EAEjH,CAAA9G,iBAAiB,aAAjBA,iBAAiB,wBAAA8F,qBAAA,GAAjB9F,iBAAiB,CAAEoE,iBAAiB,cAAA0B,qBAAA,uBAApCA,qBAAA,CAAsCuB,GAAG,CAACa,GAAG,iBAC5CjJ,OAAA;kBAAwBqI,KAAK,EAAEY,GAAG,CAACZ,KAAM;kBAAAR,QAAA,EACtCoB,GAAG,CAACN;gBAAK,GADCM,GAAG,CAACZ,KAAK;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEd,CACT,CAAC,KAAI;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAGV,KAAK,CAAC;QACJ,oBACEjI,OAAA;UAAK4H,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB7H,OAAA;YAAI4H,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAExEjI,OAAA;YAAK4H,SAAS,EAAC,qCAAqC;YAAAC,QAAA,gBAClD7H,OAAA;cAAA6H,QAAA,gBACE7H,OAAA;gBAAI4H,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvDjI,OAAA;gBAAG4H,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,IAAAf,sBAAA,GACjC/C,aAAa,CAAC/B,QAAQ,cAAA8E,sBAAA,uBAAtBA,sBAAA,CAAwB7E,IAAI,EAAC,KAAG,GAAA8E,sBAAA,GAAChD,aAAa,CAAC/B,QAAQ,cAAA+E,sBAAA,uBAAtBA,sBAAA,CAAwB7E,SAAS;cAAA;gBAAA4F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAENjI,OAAA;cAAA6H,QAAA,gBACE7H,OAAA;gBAAI4H,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrDjI,OAAA;gBAAG4H,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,IAAAb,oBAAA,GACjCjD,aAAa,CAAC3B,KAAK,cAAA4E,oBAAA,uBAAnBA,oBAAA,CAAqB/E,IAAI,EAAC,MAAI,GAAAgF,qBAAA,GAAClD,aAAa,CAAC3B,KAAK,cAAA6E,qBAAA,uBAAnBA,qBAAA,CAAqB5E,WAAW,EAAC,MACnE;cAAA;gBAAAyF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJjI,OAAA;gBAAG4H,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,GAAC,OAC9B,GAAAX,qBAAA,GAACnD,aAAa,CAAC3B,KAAK,cAAA8E,qBAAA,wBAAAC,qBAAA,GAAnBD,qBAAA,CAAqB5E,SAAS,cAAA6E,qBAAA,uBAA9BA,qBAAA,CAAgC5E,OAAO,EAAC,GAAC,GAAA6E,qBAAA,GAACrD,aAAa,CAAC3B,KAAK,cAAAgF,qBAAA,wBAAAC,qBAAA,GAAnBD,qBAAA,CAAqB9E,SAAS,cAAA+E,qBAAA,uBAA9BA,qBAAA,CAAgC7E,OAAO;cAAA;gBAAAsF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAENjI,OAAA;cAAA6H,QAAA,gBACE7H,OAAA;gBAAI4H,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjDjI,OAAA;gBAAG4H,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,GAAAP,iBAAA,GACjCvD,aAAa,CAAChB,EAAE,cAAAuE,iBAAA,uBAAhBA,iBAAA,CAAkBrF;cAAI;gBAAA6F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC,eACJjI,OAAA;gBAAG4H,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,IAAAN,kBAAA,GACjCxD,aAAa,CAAChB,EAAE,cAAAwE,kBAAA,wBAAAC,qBAAA,GAAhBD,kBAAA,CAAkBvE,QAAQ,cAAAwE,qBAAA,wBAAAC,sBAAA,GAA1BD,qBAAA,CAA4BvE,iBAAiB,cAAAwE,sBAAA,wBAAAC,sBAAA,GAA7CD,sBAAA,CAA+CtE,SAAS,cAAAuE,sBAAA,wBAAAC,sBAAA,GAAxDD,sBAAA,CAA0DrE,OAAO,cAAAsE,sBAAA,uBAAjEA,sBAAA,CAAmEuB,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,EAAC,KACxF;cAAA;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENjI,OAAA;YAAK4H,SAAS,EAAC,sDAAsD;YAAAC,QAAA,eACnE7H,OAAA;cAAK4H,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB7H,OAAA,CAACH,WAAW;gBAAC+H,SAAS,EAAC;cAAqC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/DjI,OAAA;gBAAA6H,QAAA,gBACE7H,OAAA;kBAAI4H,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,EAAC;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACxEjI,OAAA;kBAAG4H,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,EAAC;gBAGvC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAGV;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,oBACEjI,OAAA;IAAK4H,SAAS,EAAC,4EAA4E;IAAAC,QAAA,eACzF7H,OAAA;MAAK4H,SAAS,EAAC,6EAA6E;MAAAC,QAAA,gBAE1F7H,OAAA;QAAK4H,SAAS,EAAC,oCAAoC;QAAAC,QAAA,gBACjD7H,OAAA;UAAK4H,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChD7H,OAAA;YAAI4H,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAAC;UAEpD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLjI,OAAA;YACEmJ,OAAO,EAAE7I,OAAQ;YACjBsH,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAC9C;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGNjI,OAAA;UAAK4H,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnB7H,OAAA;YAAK4H,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAC/CxC,KAAK,CAAC+C,GAAG,CAAC,CAACK,IAAI,EAAEW,KAAK,kBACrBpJ,OAAA;cAAmB4H,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAC9C7H,OAAA;gBAAK4H,SAAS,EAAE,yDACdnH,WAAW,IAAIgI,IAAI,CAAC5D,EAAE,GAClB,wBAAwB,GACxB,2BAA2B,EAC9B;gBAAAgD,QAAA,EACApH,WAAW,GAAGgI,IAAI,CAAC5D,EAAE,gBACpB7E,OAAA,CAACJ,WAAW;kBAACgI,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAEnCjI,OAAA,CAACyI,IAAI,CAAClD,IAAI;kBAACqC,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cACjC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACNjI,OAAA;gBAAK4H,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,eACnC7H,OAAA;kBAAG4H,SAAS,EAAE,uBACZnH,WAAW,IAAIgI,IAAI,CAAC5D,EAAE,GAAG,eAAe,GAAG,eAAe,EACzD;kBAAAgD,QAAA,EACAY,IAAI,CAACnD;gBAAK;kBAAAwC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,EACLmB,KAAK,GAAG/D,KAAK,CAACT,MAAM,GAAG,CAAC,iBACvB5E,OAAA;gBAAK4H,SAAS,EAAE,mBACdnH,WAAW,GAAGgI,IAAI,CAAC5D,EAAE,GAAG,aAAa,GAAG,aAAa;cACpD;gBAAAiD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CACN;YAAA,GAvBOQ,IAAI,CAAC5D,EAAE;cAAAiD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAwBZ,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNjI,OAAA;QAAM0F,QAAQ,EAAEhE,YAAY,CAACgE,QAAQ,CAAE;QAACkC,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACtE7H,OAAA;UAAK4H,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAC9CvB,iBAAiB,CAAC;QAAC;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,eAGNjI,OAAA;UAAK4H,SAAS,EAAC,yDAAyD;UAAAC,QAAA,gBACtE7H,OAAA;YACEwD,IAAI,EAAC,QAAQ;YACb2F,OAAO,EAAE1D,QAAS;YAClB4D,QAAQ,EAAE5I,WAAW,KAAK,CAAE;YAC5BmH,SAAS,EAAC,2KAA2K;YAAAC,QAAA,gBAErL7H,OAAA,CAACV,WAAW;cAACsI,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,YAE1C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAETjI,OAAA;YACEwD,IAAI,EAAC,QAAQ;YACb6F,QAAQ,EAAE1I,OAAQ;YAClBiH,SAAS,EAAC,+KAA+K;YAAAC,QAAA,EAExLlH,OAAO,GACN,aAAa,GACXF,WAAW,KAAK4E,KAAK,CAACT,MAAM,GAC9B,2BAA2B,gBAE3B5E,OAAA,CAAAE,SAAA;cAAA2H,QAAA,GAAE,MAEA,eAAA7H,OAAA,CAACT,YAAY;gBAACqI,SAAS,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA,eACzC;UACH;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACzH,EAAA,CArtBIL,cAAc;EAAA,QAUyDf,OAAO;AAAA;AAAAkK,EAAA,GAV9EnJ,cAAc;AAutBpB,eAAeA,cAAc;AAAC,IAAAmJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}