import React, { useState } from 'react';
import { ChevronDown, ChevronUp, MapPin, Users, Target, Smartphone, Eye, Heart } from 'lucide-react';

const TargetingDisplay = ({ targeting }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  if (!targeting) {
    return (
      <div className="targeting-display">
        <div className="targeting-header">
          <Target size={16} />
          <span>No targeting data available</span>
        </div>
      </div>
    );
  }

  const hasTargetingData = targeting.demographics || targeting.geographic || 
                          targeting.interests?.length > 0 || targeting.behaviors?.length > 0 ||
                          targeting.custom_audiences?.length > 0 || targeting.lookalike_audiences?.length > 0;

  if (!hasTargetingData) {
    return (
      <div className="targeting-display">
        <div className="targeting-header">
          <Target size={16} />
          <span>Broad targeting (no specific criteria)</span>
        </div>
      </div>
    );
  }

  const formatAge = (demographics) => {
    if (!demographics) return null;
    const { age_min, age_max } = demographics;
    if (age_min && age_max) return `${age_min}-${age_max} years`;
    if (age_min) return `${age_min}+ years`;
    if (age_max) return `Up to ${age_max} years`;
    return null;
  };

  const formatGender = (genders) => {
    if (!genders || !Array.isArray(genders)) return null;
    const genderMap = { 1: 'Male', 2: 'Female', 3: 'All' };
    return genders.map(g => genderMap[g] || 'Unknown').join(', ');
  };

  const formatDetailedLocations = (geographic) => {
    if (!geographic) return null;

    const sections = [];

    // Countries
    if (geographic.countries && geographic.countries.length > 0) {
      sections.push({
        type: 'Countries',
        items: geographic.countries
      });
    }

    // Regions
    if (geographic.regions && geographic.regions.length > 0) {
      sections.push({
        type: 'Regions',
        items: geographic.regions.map(region => region.name || region.key || region)
      });
    }

    // Cities with radius
    if (geographic.cities && geographic.cities.length > 0) {
      sections.push({
        type: 'Cities',
        items: geographic.cities.map(city => {
          if (typeof city === 'string') return city;
          const name = city.name || city.key || 'Unknown City';
          const radius = city.radius ? ` +${city.radius}${city.distance_unit === 'kilometer' ? 'km' : 'mi'}` : '';
          return `${name}${radius}`;
        })
      });
    }

    // Custom locations with coordinates
    if (geographic.custom_locations && geographic.custom_locations.length > 0) {
      sections.push({
        type: 'Custom Locations',
        items: geographic.custom_locations.map(location => {
          const name = location.name || `${location.latitude}, ${location.longitude}`;
          const radius = location.radius ? ` +${location.radius}${location.distance_unit === 'kilometer' ? 'km' : 'mi'}` : '';
          return `${name}${radius}`;
        })
      });
    }

    // Location types
    if (geographic.location_types && geographic.location_types.length > 0) {
      const typeMap = {
        'home': 'People who live in this location',
        'recent': 'People recently in this location',
        'travel_in': 'People traveling in this location'
      };
      sections.push({
        type: 'Location Types',
        items: geographic.location_types.map(type => typeMap[type] || type)
      });
    }

    return sections.length > 0 ? sections : null;
  };

  return (
    <div className="targeting-display">
      <div 
        className="targeting-header"
        onClick={() => setIsExpanded(!isExpanded)}
        style={{ cursor: 'pointer' }}
      >
        <Target size={16} />
        <span>Targeting Information</span>
        {isExpanded ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
      </div>

      {isExpanded && (
        <div className="targeting-details">
          {/* Demographics */}
          {targeting.demographics && (
            <div className="targeting-section">
              <div className="section-header">
                <Users size={14} />
                <span>Demographics</span>
              </div>
              <div className="section-content">
                {formatAge(targeting.demographics) && (
                  <div className="targeting-item">
                    <span className="label">Age:</span>
                    <span className="value">{formatAge(targeting.demographics)}</span>
                  </div>
                )}
                {targeting.demographics.genders && (
                  <div className="targeting-item">
                    <span className="label">Gender:</span>
                    <span className="value">{formatGender(targeting.demographics.genders)}</span>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Geographic */}
          {targeting.geographic && formatDetailedLocations(targeting.geographic) && (
            <div className="targeting-section">
              <div className="section-header">
                <MapPin size={14} />
                <span>Geographic Targeting</span>
              </div>
              <div className="section-content">
                {formatDetailedLocations(targeting.geographic).map((section, sectionIndex) => (
                  <div key={sectionIndex} className="geographic-subsection">
                    <div className="subsection-title">{section.type}:</div>
                    <div className="subsection-items">
                      {section.items.map((item, itemIndex) => (
                        <span key={itemIndex} className="geographic-tag">
                          {item}
                        </span>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Interests */}
          {targeting.interests && targeting.interests.length > 0 && (
            <div className="targeting-section">
              <div className="section-header">
                <Heart size={14} />
                <span>Interest Targeting ({targeting.interests.length} interests)</span>
              </div>
              <div className="section-content">
                <div className="interests-detailed">
                  {targeting.interests.map((interest, index) => (
                    <div key={index} className="interest-item">
                      <span className="interest-name">{interest.name || interest.id}</span>
                      {interest.topic && (
                        <span className="interest-topic">({interest.topic})</span>
                      )}
                      {interest.path && interest.path.length > 0 && (
                        <div className="interest-path">
                          Path: {interest.path.join(' > ')}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Behaviors */}
          {targeting.behaviors && targeting.behaviors.length > 0 && (
            <div className="targeting-section">
              <div className="section-header">
                <Eye size={14} />
                <span>Behavioral Targeting ({targeting.behaviors.length} behaviors)</span>
              </div>
              <div className="section-content">
                <div className="behaviors-detailed">
                  {targeting.behaviors.map((behavior, index) => (
                    <div key={index} className="behavior-item">
                      <span className="behavior-name">{behavior.name || behavior.id}</span>
                      {behavior.path && behavior.path.length > 0 && (
                        <div className="behavior-path">
                          Path: {behavior.path.join(' > ')}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Custom Audiences */}
          {targeting.custom_audiences && targeting.custom_audiences.length > 0 && (
            <div className="targeting-section">
              <div className="section-header">
                <Users size={14} />
                <span>Custom Audiences ({targeting.custom_audiences.length})</span>
              </div>
              <div className="section-content">
                {targeting.custom_audiences.map((audience, index) => (
                  <div key={index} className="targeting-item">
                    <span className="value">{audience.name || audience.id}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Lookalike Audiences */}
          {targeting.lookalike_audiences && targeting.lookalike_audiences.length > 0 && (
            <div className="targeting-section">
              <div className="section-header">
                <Users size={14} />
                <span>Lookalike Audiences ({targeting.lookalike_audiences.length})</span>
              </div>
              <div className="section-content">
                {targeting.lookalike_audiences.map((audience, index) => (
                  <div key={index} className="targeting-item">
                    <span className="value">
                      {audience.name || audience.id}
                      {audience.ratio && ` (${audience.ratio}%)`}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Device Platforms */}
          {targeting.device_platforms && targeting.device_platforms.length > 0 && (
            <div className="targeting-section">
              <div className="section-header">
                <Smartphone size={14} />
                <span>Device Platforms</span>
              </div>
              <div className="section-content">
                <div className="platforms-list">
                  {targeting.device_platforms.map((platform, index) => (
                    <span key={index} className="platform-tag">
                      {platform}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Detailed Placements */}
          {(targeting.placements?.length > 0 || targeting.facebook_positions?.length > 0 ||
            targeting.instagram_positions?.length > 0 || targeting.messenger_positions?.length > 0) && (
            <div className="targeting-section">
              <div className="section-header">
                <Eye size={14} />
                <span>Ad Placements</span>
              </div>
              <div className="section-content">
                {/* General placements */}
                {targeting.placements && targeting.placements.length > 0 && (
                  <div className="placement-subsection">
                    <div className="subsection-title">Platforms:</div>
                    <div className="subsection-items">
                      {targeting.placements.map((placement, index) => (
                        <span key={index} className="placement-tag platform">
                          {placement}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {/* Facebook specific positions */}
                {targeting.facebook_positions && targeting.facebook_positions.length > 0 && (
                  <div className="placement-subsection">
                    <div className="subsection-title">Facebook Placements:</div>
                    <div className="subsection-items">
                      {targeting.facebook_positions.map((position, index) => (
                        <span key={index} className="placement-tag facebook">
                          {position.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {/* Instagram specific positions */}
                {targeting.instagram_positions && targeting.instagram_positions.length > 0 && (
                  <div className="placement-subsection">
                    <div className="subsection-title">Instagram Placements:</div>
                    <div className="subsection-items">
                      {targeting.instagram_positions.map((position, index) => (
                        <span key={index} className="placement-tag instagram">
                          {position.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {/* Messenger specific positions */}
                {targeting.messenger_positions && targeting.messenger_positions.length > 0 && (
                  <div className="placement-subsection">
                    <div className="subsection-title">Messenger Placements:</div>
                    <div className="subsection-items">
                      {targeting.messenger_positions.map((position, index) => (
                        <span key={index} className="placement-tag messenger">
                          {position.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Automation & Advanced Settings */}
          {targeting.automation && (targeting.automation.advantage_audience !== undefined ||
            targeting.automation.targeting_automation) && (
            <div className="targeting-section">
              <div className="section-header">
                <Target size={14} />
                <span>Automation & Advanced Settings</span>
              </div>
              <div className="section-content">
                {targeting.automation.advantage_audience !== undefined && (
                  <div className="automation-item">
                    <span className="label">Advantage+ Audience:</span>
                    <span className={`value ${targeting.automation.advantage_audience ? 'enabled' : 'disabled'}`}>
                      {targeting.automation.advantage_audience ? 'Enabled' : 'Disabled'}
                    </span>
                    {targeting.automation.advantage_audience && (
                      <div className="automation-description">
                        Facebook can expand your audience to find people likely to be interested in your ads
                      </div>
                    )}
                  </div>
                )}

                {targeting.automation.targeting_automation && (
                  <div className="automation-item">
                    <span className="label">Targeting Automation:</span>
                    <span className="value">{JSON.stringify(targeting.automation.targeting_automation)}</span>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default TargetingDisplay;
