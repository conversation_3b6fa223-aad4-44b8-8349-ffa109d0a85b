# Pressure Max API

Production API for Facebook Marketing automation with smart caching and rate limiting.

## 🚀 Features

- **Facebook Marketing API Integration** - Complete campaign, ad set, and ad management
- **Smart Rate Limiting** - Intelligent API call optimization (10s delays)
- **Advanced Caching** - 60-minute cache with automatic invalidation
- **Smart Batching** - Single API calls fetch complete nested data
- **Real-time Updates** - Cache invalidation ensures fresh data after changes
- **CORS Support** - Frontend integration ready
- **Health Monitoring** - Built-in health check endpoint

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   React         │    │   Pressure Max  │    │   Facebook      │
│   Frontend      │◄──►│      API        │◄──►│ Marketing API   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │   In-Memory     │
                       │     Cache       │
                       └─────────────────┘
```

## 📋 Prerequisites

- Node.js 18+
- Facebook Developer Account with Marketing API access

## 🛠️ Installation

1. **Clone and setup:**
   ```bash
   cd pressure-max-api
   npm install
   ```

2. **Environment Configuration:**
   ```bash
   # Create .env file with your Facebook access token
   echo "FACEBOOK_ACCESS_TOKEN=your_token_here" > .env
   ```

3. **Start the API:**
   ```bash
   # Development with auto-restart
   npm run dev

   # Production
   npm start
   ```

## 🔧 Configuration

### Environment Variables

```env
# Optional - Facebook access token (hardcoded in server for development)
FACEBOOK_ACCESS_TOKEN=your-facebook-access-token

# Optional - Server configuration
PORT=3000
NODE_ENV=development
```

## 📚 API Documentation

Once running, visit:
- **Health Check**: http://localhost:3000/health
- **Ad Accounts**: http://localhost:3000/api/v1/facebook/ad-accounts
- **Campaign Objectives**: http://localhost:3000/api/v1/facebook/campaign-objectives

## 📊 API Endpoints

### System
- `GET /health` - Health check and server status
- `POST /api/v1/cache/clear` - Clear all cache entries

### Facebook Integration
- `GET /api/v1/facebook/oauth-url` - Get OAuth URL
- `POST /api/v1/facebook/oauth-callback` - Handle OAuth callback
- `GET /api/v1/facebook/ad-accounts` - Get user's ad accounts
- `GET /api/v1/facebook/pages` - Get user's pages
- `GET /api/v1/facebook/campaign-objectives` - Get valid campaign objectives
- `POST /api/v1/facebook/campaigns` - Create campaign
- `GET /api/v1/facebook/campaigns/:adAccountId` - Get campaigns with smart batching
- `GET /api/v1/facebook/adsets/:adAccountId` - Get ad sets
- `GET /api/v1/facebook/ads/:adAccountId` - Get ads
- `GET /api/v1/facebook/leadforms/:adAccountId` - Get lead forms
- `GET /api/v1/facebook/audiences/:adAccountId` - Get custom audiences

## 🔄 Real-time Features

WebSocket connection for real-time updates:

```javascript
const socket = io('http://localhost:3000', {
  auth: {
    token: 'your-jwt-token'
  }
});

// Subscribe to campaign updates
socket.emit('subscribe:campaigns', [campaignId]);

// Listen for lead updates
socket.on('new_lead', (lead) => {
  console.log('New lead received:', lead);
});
```

## 🧪 Testing

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

## 📝 Database Migrations

```bash
# Create new migration
npx knex migrate:make migration_name

# Run migrations
npm run migrate

# Rollback last migration
npm run migrate:rollback
```

## 🚀 Deployment

### Docker Deployment
```bash
# Build image
docker build -t pressure-max-api .

# Run with docker-compose
docker-compose up -d
```

### Environment Setup
1. Set all required environment variables
2. Ensure PostgreSQL and Redis are accessible
3. Run database migrations
4. Configure reverse proxy (nginx/Apache)
5. Set up SSL certificates
6. Configure monitoring and logging

## 📊 Monitoring

The API includes built-in monitoring:
- Health check endpoint: `/health`
- Structured logging with Winston
- Performance metrics
- Error tracking
- Audit trails

## 🔒 Security Features

- JWT token authentication
- Rate limiting per user/endpoint
- Input validation and sanitization
- CORS protection
- Helmet security headers
- SQL injection prevention
- XSS protection
- Token blacklisting

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Run linting: `npm run lint`
6. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details.

## 🆘 Support

For support and questions:
- Check the API documentation at `/api-docs`
- Review the logs in `logs/app.log`
- Open an issue on GitHub
