{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FB API SCRAPER\\\\pressure-max-frontend\\\\src\\\\components\\\\CampaignSection.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport api, { facebookAPI } from '../services/api';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useForm } from 'react-hook-form';\nimport toast from 'react-hot-toast';\nimport { Target, Plus, DollarSign, Calendar, AlertCircle, Zap } from 'lucide-react';\nimport TargetingDisplay from './TargetingDisplay';\nimport CreativeDisplay from './CreativeDisplay';\nimport CampaignWizard from './CampaignWizard';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst CAMPAIGN_OBJECTIVES = [\n// New OUTCOME-based objectives (recommended)\n{\n  value: 'OUTCOME_TRAFFIC',\n  label: 'Traffic'\n}, {\n  value: 'OUTCOME_ENGAGEMENT',\n  label: 'Engagement'\n}, {\n  value: 'OUTCOME_LEADS',\n  label: 'Lead Generation'\n}, {\n  value: 'OUTCOME_SALES',\n  label: 'Sales'\n}, {\n  value: 'OUTCOME_AWARENESS',\n  label: 'Awareness'\n}, {\n  value: 'OUTCOME_APP_PROMOTION',\n  label: 'App Promotion'\n},\n// Legacy objectives (still supported but deprecated)\n{\n  value: 'REACH',\n  label: 'Reach (Legacy)'\n}, {\n  value: 'CONVERSIONS',\n  label: 'Conversions (Legacy)'\n}, {\n  value: 'BRAND_AWARENESS',\n  label: 'Brand Awareness (Legacy)'\n}, {\n  value: 'MESSAGES',\n  label: 'Messages (Legacy)'\n}, {\n  value: 'VIDEO_VIEWS',\n  label: 'Video Views (Legacy)'\n}, {\n  value: 'STORE_VISITS',\n  label: 'Store Visits (Legacy)'\n}];\nconst CampaignSection = () => {\n  _s();\n  const {\n    isAuthenticated\n  } = useAuth();\n  const [campaigns, setCampaigns] = useState([]);\n  const [adSets, setAdSets] = useState([]);\n  const [ads, setAds] = useState([]);\n  const [adAccounts, setAdAccounts] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [showCreateForm, setShowCreateForm] = useState(false);\n  const [showCampaignWizard, setShowCampaignWizard] = useState(false);\n  const [selectedAccount, setSelectedAccount] = useState('');\n  const [activeTab, setActiveTab] = useState('campaigns');\n  const [loadedData, setLoadedData] = useState({\n    campaigns: false,\n    adsets: false,\n    ads: false\n  });\n  const {\n    register,\n    handleSubmit,\n    reset,\n    formState: {\n      errors\n    }\n  } = useForm();\n  useEffect(() => {\n    if (isAuthenticated) {\n      loadAdAccounts();\n    }\n  }, [isAuthenticated]);\n  useEffect(() => {\n    if (selectedAccount) {\n      // Reset loaded data when account changes\n      setLoadedData({\n        campaigns: false,\n        adsets: false,\n        ads: false\n      });\n      // Only load campaigns by default when account is selected\n      loadCampaigns();\n    }\n  }, [selectedAccount]);\n\n  // Load data when tab changes (smart batching means we get everything at once)\n  useEffect(() => {\n    if (selectedAccount && activeTab) {\n      // With smart batching, we get all data when campaigns are loaded\n      // So we only need to check if campaigns are loaded\n      if (!loadedData.campaigns) {\n        loadCampaigns(); // This loads everything: campaigns, ad sets, and ads\n      }\n    }\n  }, [activeTab, selectedAccount, loadedData]);\n  const loadAdAccounts = async () => {\n    try {\n      var _response$data;\n      // Use the direct Facebook API endpoint that works with the access token\n      const response = await api.get('/facebook/ad-accounts');\n      setAdAccounts(response.data || []);\n      if (((_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.length) > 0) {\n        setSelectedAccount(response.data[0].id);\n      }\n    } catch (error) {\n      console.error('Error loading ad accounts:', error);\n      toast.error('Failed to load ad accounts');\n    }\n  };\n  const loadCampaigns = async () => {\n    if (!selectedAccount) return;\n    try {\n      setLoading(true);\n      console.log('🚀 Loading complete account data with smart batching...');\n\n      // Use the direct Facebook API endpoint that works with the access token\n      const response = await api.get(`/facebook/campaigns/${selectedAccount}`);\n      const completeData = response.data || [];\n\n      // Extract campaigns, ad sets, and ads from the nested structure\n      setCampaigns(completeData);\n\n      // Extract ad sets from all campaigns\n      const allAdSets = completeData.flatMap(campaign => (campaign.adsets || []).map(adset => ({\n        ...adset,\n        campaign_id: campaign.id,\n        campaign_name: campaign.name\n      })));\n      setAdSets(allAdSets);\n\n      // Extract ads from all ad sets\n      const allAds = allAdSets.flatMap(adset => (adset.ads || []).map(ad => ({\n        ...ad,\n        adset_id: adset.id,\n        adset_name: adset.name,\n        campaign_id: adset.campaign_id,\n        campaign_name: adset.campaign_name\n      })));\n      setAds(allAds);\n\n      // Mark all data as loaded since we got everything in one call\n      setLoadedData({\n        campaigns: true,\n        adsets: true,\n        ads: true\n      });\n      console.log('✅ Smart batching complete!', {\n        campaigns: completeData.length,\n        adSets: allAdSets.length,\n        ads: allAds.length\n      });\n    } catch (error) {\n      toast.error('Failed to load account data');\n      setCampaigns([]);\n      setAdSets([]);\n      setAds([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Note: loadAdSets and loadAds functions removed - smart batching gets everything in loadCampaigns\n\n  const handleWizardSuccess = result => {\n    console.log('Campaign hierarchy created:', result);\n    toast.success('Campaign hierarchy created successfully!');\n    setShowCampaignWizard(false);\n    // Refresh campaigns data\n    if (selectedAccount) {\n      loadCampaigns(selectedAccount);\n    }\n  };\n  const onCreateCampaign = async data => {\n    try {\n      setLoading(true);\n      const campaignData = {\n        adAccountId: selectedAccount,\n        name: data.name,\n        objective: data.objective,\n        status: 'PAUSED',\n        // Always start paused for safety\n        specialAdCategories: data.specialAdCategories ? [data.specialAdCategories] : []\n      };\n\n      // Use the direct Facebook API endpoint that works with the access token\n      await api.post('/facebook/campaigns', campaignData);\n      toast.success('Campaign created successfully!');\n\n      // Reset form and reload campaigns\n      reset();\n      setShowCreateForm(false);\n      loadCampaigns();\n    } catch (error) {\n      var _error$response, _error$response$data;\n      toast.error(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Failed to create campaign');\n    } finally {\n      setLoading(false);\n    }\n  };\n  if (!isAuthenticated) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"campaign-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Campaign Management\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"auth-required\",\n        children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Please log in to manage campaigns\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 191,\n      columnNumber: 7\n    }, this);\n  }\n  if (adAccounts.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"campaign-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Campaign Management\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"no-accounts\",\n        children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"No Facebook ad accounts found. Please connect your Facebook account first.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 203,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"campaign-section\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"Campaign Management\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 215,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"account-selector\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        children: \"Select Ad Account:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 218,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n        value: selectedAccount,\n        onChange: e => setSelectedAccount(e.target.value),\n        children: adAccounts.map(account => /*#__PURE__*/_jsxDEV(\"option\", {\n          value: account.id,\n          children: [account.name, \" (\", account.id, \")\"]\n        }, account.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 219,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 217,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"facebook-tabs\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        className: activeTab === 'campaigns' ? 'active' : '',\n        onClick: () => setActiveTab('campaigns'),\n        children: [/*#__PURE__*/_jsxDEV(Target, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 11\n        }, this), \"Campaigns (\", campaigns.length, \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: activeTab === 'adsets' ? 'active' : '',\n        onClick: () => setActiveTab('adsets'),\n        children: [/*#__PURE__*/_jsxDEV(DollarSign, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 11\n        }, this), \"Ad Sets (\", adSets.length, \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: activeTab === 'ads' ? 'active' : '',\n        onClick: () => setActiveTab('ads'),\n        children: [/*#__PURE__*/_jsxDEV(Calendar, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 11\n        }, this), \"Ads (\", ads.length, \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 231,\n      columnNumber: 7\n    }, this), activeTab === 'campaigns' && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"campaigns-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: [/*#__PURE__*/_jsxDEV(Target, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 11\n          }, this), \"Campaigns (\", campaigns.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowCampaignWizard(true),\n            className: \"create-btn bg-blue-600 hover:bg-blue-700 text-white\",\n            disabled: !selectedAccount,\n            children: [/*#__PURE__*/_jsxDEV(Zap, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 13\n            }, this), \"Campaign Wizard\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowCreateForm(!showCreateForm),\n            className: \"create-btn\",\n            children: [/*#__PURE__*/_jsxDEV(Plus, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 13\n            }, this), showCreateForm ? 'Cancel' : 'Quick Campaign']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 257,\n        columnNumber: 11\n      }, this), showCreateForm && /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit(onCreateCampaign),\n        className: \"create-campaign-form\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          children: \"Create New Campaign\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Campaign Name:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            ...register('name', {\n              required: 'Campaign name is required'\n            }),\n            placeholder: \"Enter campaign name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 13\n          }, this), errors.name && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"error\",\n            children: errors.name.message\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Objective:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            ...register('objective', {\n              required: 'Objective is required'\n            }),\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"Select objective\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 15\n            }, this), CAMPAIGN_OBJECTIVES.map(obj => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: obj.value,\n              children: obj.label\n            }, obj.value, false, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 13\n          }, this), errors.objective && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"error\",\n            children: errors.objective.message\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 34\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Special Ad Categories (optional):\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            ...register('specialAdCategories'),\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"None\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"CREDIT\",\n              children: \"Credit\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 312,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"EMPLOYMENT\",\n              children: \"Employment\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"HOUSING\",\n              children: \"Housing\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"ISSUES_ELECTIONS_POLITICS\",\n              children: \"Issues, Elections or Politics\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            disabled: loading,\n            className: \"submit-btn\",\n            children: loading ? 'Creating...' : 'Create Campaign'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: () => setShowCreateForm(false),\n            className: \"cancel-btn\",\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 282,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"campaigns-list\",\n        children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading\",\n          children: \"Loading campaigns...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 336,\n          columnNumber: 11\n        }, this) : campaigns.length > 0 ? campaigns.map(campaign => {\n          var _campaign$status;\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"campaign-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"campaign-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                children: campaign.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 341,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `campaign-status ${(_campaign$status = campaign.status) === null || _campaign$status === void 0 ? void 0 : _campaign$status.toLowerCase()}`,\n                children: campaign.status\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 342,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"campaign-details\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"detail-item\",\n                children: [/*#__PURE__*/_jsxDEV(Target, {\n                  size: 14\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 348,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"Objective: \", campaign.objective]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 349,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 347,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"detail-item\",\n                children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                  size: 14\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 352,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"Created: \", new Date(campaign.created_time).toLocaleDateString()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 353,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 351,\n                columnNumber: 17\n              }, this), campaign.daily_budget && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"detail-item\",\n                children: [/*#__PURE__*/_jsxDEV(DollarSign, {\n                  size: 14\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 357,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"Daily Budget: $\", (campaign.daily_budget / 100).toFixed(2)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 358,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 356,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 346,\n              columnNumber: 15\n            }, this)]\n          }, campaign.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 13\n          }, this);\n        }) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"no-campaigns\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"No campaigns found for this ad account.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Create your first campaign using the form above.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 367,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 365,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 334,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true), activeTab === 'adsets' && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"adsets-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Ad Sets\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 376,\n        columnNumber: 11\n      }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading\",\n        children: \"Loading ad sets...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 378,\n        columnNumber: 13\n      }, this) : adSets.length > 0 ? adSets.map(adSet => {\n        var _adSet$status;\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"adset-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"adset-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: adSet.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 383,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `status ${(_adSet$status = adSet.status) === null || _adSet$status === void 0 ? void 0 : _adSet$status.toLowerCase()}`,\n              children: adSet.status\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 384,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 382,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"adset-details\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Campaign: \", adSet.campaign_id]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 390,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 389,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                size: 14\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 393,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Created: \", new Date(adSet.created_time).toLocaleDateString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 394,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 392,\n              columnNumber: 19\n            }, this), adSet.daily_budget && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(DollarSign, {\n                size: 14\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 398,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Daily Budget: $\", (adSet.daily_budget / 100).toFixed(2)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 399,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 397,\n              columnNumber: 21\n            }, this), adSet.optimization_goal && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(Target, {\n                size: 14\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 404,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Optimization: \", adSet.optimization_goal]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 405,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 403,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 388,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(TargetingDisplay, {\n            targeting: adSet.targeting_formatted\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 411,\n            columnNumber: 17\n          }, this)]\n        }, adSet.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 381,\n          columnNumber: 15\n        }, this);\n      }) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"no-adsets\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"No ad sets found for this ad account.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 416,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 415,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 375,\n      columnNumber: 9\n    }, this), activeTab === 'ads' && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"ads-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Ads\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 424,\n        columnNumber: 11\n      }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading\",\n        children: \"Loading ads...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 426,\n        columnNumber: 13\n      }, this) : ads.length > 0 ? ads.map(ad => {\n        var _ad$status;\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"ad-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ad-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: ad.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 431,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `status ${(_ad$status = ad.status) === null || _ad$status === void 0 ? void 0 : _ad$status.toLowerCase()}`,\n              children: ad.status\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 432,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 430,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ad-details\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Ad Set: \", ad.adset_id]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 438,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 437,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Campaign: \", ad.campaign_id]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 441,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 440,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                size: 14\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 444,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Created: \", new Date(ad.created_time).toLocaleDateString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 445,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 443,\n              columnNumber: 19\n            }, this), ad.insights && ad.insights.impressions && ad.insights.impressions !== '0' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Impressions: \", parseInt(ad.insights.impressions).toLocaleString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 449,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 448,\n              columnNumber: 21\n            }, this), ad.insights && ad.insights.clicks && ad.insights.clicks !== '0' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Clicks: \", parseInt(ad.insights.clicks).toLocaleString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 454,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 453,\n              columnNumber: 21\n            }, this), ad.insights && ad.insights.spend && ad.insights.spend !== '0.00' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(DollarSign, {\n                size: 14\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 459,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Spend: $\", parseFloat(ad.insights.spend).toFixed(2)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 460,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 458,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 436,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(CreativeDisplay, {\n            creative: ad.creative_formatted\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 466,\n            columnNumber: 17\n          }, this)]\n        }, ad.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 429,\n          columnNumber: 15\n        }, this);\n      }) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"no-ads\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"No ads found for this ad account.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 471,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 470,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 423,\n      columnNumber: 9\n    }, this), showCampaignWizard && /*#__PURE__*/_jsxDEV(CampaignWizard, {\n      adAccounts: adAccounts,\n      selectedAccount: selectedAccount,\n      onClose: () => setShowCampaignWizard(false),\n      onSuccess: handleWizardSuccess\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 479,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 214,\n    columnNumber: 5\n  }, this);\n};\n_s(CampaignSection, \"fwrGi9jlAlk3Ehb7foUi+qiD/+o=\", false, function () {\n  return [useAuth, useForm];\n});\n_c = CampaignSection;\nexport default CampaignSection;\nvar _c;\n$RefreshReg$(_c, \"CampaignSection\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "api", "facebookAPI", "useAuth", "useForm", "toast", "Target", "Plus", "DollarSign", "Calendar", "AlertCircle", "Zap", "TargetingDisplay", "CreativeDisplay", "CampaignWizard", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CAMPAIGN_OBJECTIVES", "value", "label", "CampaignSection", "_s", "isAuthenticated", "campaigns", "setCampaigns", "adSets", "setAdSets", "ads", "setAds", "adAccounts", "setAdAccounts", "loading", "setLoading", "showCreateForm", "setShowCreateForm", "showCampaignWizard", "setShowCampaignWizard", "selectedAccount", "setSelectedAccount", "activeTab", "setActiveTab", "loadedData", "setLoadedData", "adsets", "register", "handleSubmit", "reset", "formState", "errors", "loadAdAccounts", "loadCampaigns", "_response$data", "response", "get", "data", "length", "id", "error", "console", "log", "completeData", "allAdSets", "flatMap", "campaign", "map", "adset", "campaign_id", "campaign_name", "name", "allAds", "ad", "adset_id", "adset_name", "handleWizardSuccess", "result", "success", "onCreateCampaign", "campaignData", "adAccountId", "objective", "status", "specialAdCategories", "post", "_error$response", "_error$response$data", "message", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "onChange", "e", "target", "account", "onClick", "disabled", "onSubmit", "type", "required", "placeholder", "obj", "_campaign$status", "toLowerCase", "Date", "created_time", "toLocaleDateString", "daily_budget", "toFixed", "adSet", "_adSet$status", "optimization_goal", "targeting", "targeting_formatted", "_ad$status", "insights", "impressions", "parseInt", "toLocaleString", "clicks", "spend", "parseFloat", "creative", "creative_formatted", "onClose", "onSuccess", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/FB API SCRAPER/pressure-max-frontend/src/components/CampaignSection.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport api, { facebookAPI } from '../services/api';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useForm } from 'react-hook-form';\nimport toast from 'react-hot-toast';\nimport { Target, Plus, DollarSign, Calendar, AlertCircle, Zap } from 'lucide-react';\nimport TargetingDisplay from './TargetingDisplay';\nimport CreativeDisplay from './CreativeDisplay';\nimport CampaignWizard from './CampaignWizard';\n\nconst CAMPAIGN_OBJECTIVES = [\n  // New OUTCOME-based objectives (recommended)\n  { value: 'OUTCOME_TRAFFIC', label: 'Traffic' },\n  { value: 'OUTCOME_ENGAGEMENT', label: 'Engagement' },\n  { value: 'OUTCOME_LEADS', label: 'Lead Generation' },\n  { value: 'OUTCOME_SALES', label: 'Sales' },\n  { value: 'OUTCOME_AWARENESS', label: 'Awareness' },\n  { value: 'OUTCOME_APP_PROMOTION', label: 'App Promotion' },\n\n  // Legacy objectives (still supported but deprecated)\n  { value: 'REACH', label: 'Reach (Legacy)' },\n  { value: 'CONVERSIONS', label: 'Conversions (Legacy)' },\n  { value: 'BRAND_AWARENESS', label: 'Brand Awareness (Legacy)' },\n  { value: 'MESSAGES', label: 'Messages (Legacy)' },\n  { value: 'VIDEO_VIEWS', label: 'Video Views (Legacy)' },\n  { value: 'STORE_VISITS', label: 'Store Visits (Legacy)' }\n];\n\nconst CampaignSection = () => {\n  const { isAuthenticated } = useAuth();\n  const [campaigns, setCampaigns] = useState([]);\n  const [adSets, setAdSets] = useState([]);\n  const [ads, setAds] = useState([]);\n  const [adAccounts, setAdAccounts] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [showCreateForm, setShowCreateForm] = useState(false);\n  const [showCampaignWizard, setShowCampaignWizard] = useState(false);\n  const [selectedAccount, setSelectedAccount] = useState('');\n  const [activeTab, setActiveTab] = useState('campaigns');\n  const [loadedData, setLoadedData] = useState({\n    campaigns: false,\n    adsets: false,\n    ads: false\n  });\n\n  const { register, handleSubmit, reset, formState: { errors } } = useForm();\n\n  useEffect(() => {\n    if (isAuthenticated) {\n      loadAdAccounts();\n    }\n  }, [isAuthenticated]);\n\n  useEffect(() => {\n    if (selectedAccount) {\n      // Reset loaded data when account changes\n      setLoadedData({\n        campaigns: false,\n        adsets: false,\n        ads: false\n      });\n      // Only load campaigns by default when account is selected\n      loadCampaigns();\n    }\n  }, [selectedAccount]);\n\n  // Load data when tab changes (smart batching means we get everything at once)\n  useEffect(() => {\n    if (selectedAccount && activeTab) {\n      // With smart batching, we get all data when campaigns are loaded\n      // So we only need to check if campaigns are loaded\n      if (!loadedData.campaigns) {\n        loadCampaigns(); // This loads everything: campaigns, ad sets, and ads\n      }\n    }\n  }, [activeTab, selectedAccount, loadedData]);\n\n  const loadAdAccounts = async () => {\n    try {\n      // Use the direct Facebook API endpoint that works with the access token\n      const response = await api.get('/facebook/ad-accounts');\n      setAdAccounts(response.data || []);\n      if (response.data?.length > 0) {\n        setSelectedAccount(response.data[0].id);\n      }\n    } catch (error) {\n      console.error('Error loading ad accounts:', error);\n      toast.error('Failed to load ad accounts');\n    }\n  };\n\n  const loadCampaigns = async () => {\n    if (!selectedAccount) return;\n\n    try {\n      setLoading(true);\n      console.log('🚀 Loading complete account data with smart batching...');\n\n      // Use the direct Facebook API endpoint that works with the access token\n      const response = await api.get(`/facebook/campaigns/${selectedAccount}`);\n      const completeData = response.data || [];\n\n      // Extract campaigns, ad sets, and ads from the nested structure\n      setCampaigns(completeData);\n\n      // Extract ad sets from all campaigns\n      const allAdSets = completeData.flatMap(campaign =>\n        (campaign.adsets || []).map(adset => ({\n          ...adset,\n          campaign_id: campaign.id,\n          campaign_name: campaign.name\n        }))\n      );\n      setAdSets(allAdSets);\n\n      // Extract ads from all ad sets\n      const allAds = allAdSets.flatMap(adset =>\n        (adset.ads || []).map(ad => ({\n          ...ad,\n          adset_id: adset.id,\n          adset_name: adset.name,\n          campaign_id: adset.campaign_id,\n          campaign_name: adset.campaign_name\n        }))\n      );\n      setAds(allAds);\n\n      // Mark all data as loaded since we got everything in one call\n      setLoadedData({\n        campaigns: true,\n        adsets: true,\n        ads: true\n      });\n\n      console.log('✅ Smart batching complete!', {\n        campaigns: completeData.length,\n        adSets: allAdSets.length,\n        ads: allAds.length\n      });\n\n    } catch (error) {\n      toast.error('Failed to load account data');\n      setCampaigns([]);\n      setAdSets([]);\n      setAds([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Note: loadAdSets and loadAds functions removed - smart batching gets everything in loadCampaigns\n\n  const handleWizardSuccess = (result) => {\n    console.log('Campaign hierarchy created:', result);\n    toast.success('Campaign hierarchy created successfully!');\n    setShowCampaignWizard(false);\n    // Refresh campaigns data\n    if (selectedAccount) {\n      loadCampaigns(selectedAccount);\n    }\n  };\n\n  const onCreateCampaign = async (data) => {\n    try {\n      setLoading(true);\n      const campaignData = {\n        adAccountId: selectedAccount,\n        name: data.name,\n        objective: data.objective,\n        status: 'PAUSED', // Always start paused for safety\n        specialAdCategories: data.specialAdCategories ? [data.specialAdCategories] : []\n      };\n\n      // Use the direct Facebook API endpoint that works with the access token\n      await api.post('/facebook/campaigns', campaignData);\n      toast.success('Campaign created successfully!');\n\n      // Reset form and reload campaigns\n      reset();\n      setShowCreateForm(false);\n      loadCampaigns();\n    } catch (error) {\n      toast.error(error.response?.data?.message || 'Failed to create campaign');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (!isAuthenticated) {\n    return (\n      <div className=\"campaign-section\">\n        <h2>Campaign Management</h2>\n        <div className=\"auth-required\">\n          <AlertCircle size={20} />\n          <p>Please log in to manage campaigns</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (adAccounts.length === 0) {\n    return (\n      <div className=\"campaign-section\">\n        <h2>Campaign Management</h2>\n        <div className=\"no-accounts\">\n          <AlertCircle size={20} />\n          <p>No Facebook ad accounts found. Please connect your Facebook account first.</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"campaign-section\">\n      <h2>Campaign Management</h2>\n      \n      <div className=\"account-selector\">\n        <label>Select Ad Account:</label>\n        <select \n          value={selectedAccount} \n          onChange={(e) => setSelectedAccount(e.target.value)}\n        >\n          {adAccounts.map((account) => (\n            <option key={account.id} value={account.id}>\n              {account.name} ({account.id})\n            </option>\n          ))}\n        </select>\n      </div>\n\n      <div className=\"facebook-tabs\">\n        <button\n          className={activeTab === 'campaigns' ? 'active' : ''}\n          onClick={() => setActiveTab('campaigns')}\n        >\n          <Target size={16} />\n          Campaigns ({campaigns.length})\n        </button>\n        <button\n          className={activeTab === 'adsets' ? 'active' : ''}\n          onClick={() => setActiveTab('adsets')}\n        >\n          <DollarSign size={16} />\n          Ad Sets ({adSets.length})\n        </button>\n        <button\n          className={activeTab === 'ads' ? 'active' : ''}\n          onClick={() => setActiveTab('ads')}\n        >\n          <Calendar size={16} />\n          Ads ({ads.length})\n        </button>\n      </div>\n\n      {activeTab === 'campaigns' && (\n        <>\n          <div className=\"campaigns-header\">\n        <h3>\n          <Target size={16} />\n          Campaigns ({campaigns.length})\n        </h3>\n        <div className=\"flex gap-2\">\n          <button\n            onClick={() => setShowCampaignWizard(true)}\n            className=\"create-btn bg-blue-600 hover:bg-blue-700 text-white\"\n            disabled={!selectedAccount}\n          >\n            <Zap size={16} />\n            Campaign Wizard\n          </button>\n          <button\n            onClick={() => setShowCreateForm(!showCreateForm)}\n            className=\"create-btn\"\n          >\n            <Plus size={16} />\n            {showCreateForm ? 'Cancel' : 'Quick Campaign'}\n          </button>\n        </div>\n      </div>\n\n      {showCreateForm && (\n        <form onSubmit={handleSubmit(onCreateCampaign)} className=\"create-campaign-form\">\n          <h4>Create New Campaign</h4>\n          \n          <div className=\"form-group\">\n            <label>Campaign Name:</label>\n            <input\n              type=\"text\"\n              {...register('name', { required: 'Campaign name is required' })}\n              placeholder=\"Enter campaign name\"\n            />\n            {errors.name && <span className=\"error\">{errors.name.message}</span>}\n          </div>\n\n          <div className=\"form-group\">\n            <label>Objective:</label>\n            <select {...register('objective', { required: 'Objective is required' })}>\n              <option value=\"\">Select objective</option>\n              {CAMPAIGN_OBJECTIVES.map((obj) => (\n                <option key={obj.value} value={obj.value}>\n                  {obj.label}\n                </option>\n              ))}\n            </select>\n            {errors.objective && <span className=\"error\">{errors.objective.message}</span>}\n          </div>\n\n          <div className=\"form-group\">\n            <label>Special Ad Categories (optional):</label>\n            <select {...register('specialAdCategories')}>\n              <option value=\"\">None</option>\n              <option value=\"CREDIT\">Credit</option>\n              <option value=\"EMPLOYMENT\">Employment</option>\n              <option value=\"HOUSING\">Housing</option>\n              <option value=\"ISSUES_ELECTIONS_POLITICS\">Issues, Elections or Politics</option>\n            </select>\n          </div>\n\n          <div className=\"form-actions\">\n            <button type=\"submit\" disabled={loading} className=\"submit-btn\">\n              {loading ? 'Creating...' : 'Create Campaign'}\n            </button>\n            <button \n              type=\"button\" \n              onClick={() => setShowCreateForm(false)}\n              className=\"cancel-btn\"\n            >\n              Cancel\n            </button>\n          </div>\n        </form>\n      )}\n\n      <div className=\"campaigns-list\">\n        {loading ? (\n          <div className=\"loading\">Loading campaigns...</div>\n        ) : campaigns.length > 0 ? (\n          campaigns.map((campaign) => (\n            <div key={campaign.id} className=\"campaign-item\">\n              <div className=\"campaign-header\">\n                <h4>{campaign.name}</h4>\n                <span className={`campaign-status ${campaign.status?.toLowerCase()}`}>\n                  {campaign.status}\n                </span>\n              </div>\n              <div className=\"campaign-details\">\n                <div className=\"detail-item\">\n                  <Target size={14} />\n                  <span>Objective: {campaign.objective}</span>\n                </div>\n                <div className=\"detail-item\">\n                  <Calendar size={14} />\n                  <span>Created: {new Date(campaign.created_time).toLocaleDateString()}</span>\n                </div>\n                {campaign.daily_budget && (\n                  <div className=\"detail-item\">\n                    <DollarSign size={14} />\n                    <span>Daily Budget: ${(campaign.daily_budget / 100).toFixed(2)}</span>\n                  </div>\n                )}\n              </div>\n            </div>\n          ))\n        ) : (\n          <div className=\"no-campaigns\">\n            <p>No campaigns found for this ad account.</p>\n            <p>Create your first campaign using the form above.</p>\n          </div>\n        )}\n      </div>\n        </>\n      )}\n\n      {activeTab === 'adsets' && (\n        <div className=\"adsets-section\">\n          <h3>Ad Sets</h3>\n          {loading ? (\n            <div className=\"loading\">Loading ad sets...</div>\n          ) : adSets.length > 0 ? (\n            adSets.map((adSet) => (\n              <div key={adSet.id} className=\"adset-item\">\n                <div className=\"adset-header\">\n                  <strong>{adSet.name}</strong>\n                  <span className={`status ${adSet.status?.toLowerCase()}`}>\n                    {adSet.status}\n                  </span>\n                </div>\n                <div className=\"adset-details\">\n                  <div className=\"detail-item\">\n                    <span>Campaign: {adSet.campaign_id}</span>\n                  </div>\n                  <div className=\"detail-item\">\n                    <Calendar size={14} />\n                    <span>Created: {new Date(adSet.created_time).toLocaleDateString()}</span>\n                  </div>\n                  {adSet.daily_budget && (\n                    <div className=\"detail-item\">\n                      <DollarSign size={14} />\n                      <span>Daily Budget: ${(adSet.daily_budget / 100).toFixed(2)}</span>\n                    </div>\n                  )}\n                  {adSet.optimization_goal && (\n                    <div className=\"detail-item\">\n                      <Target size={14} />\n                      <span>Optimization: {adSet.optimization_goal}</span>\n                    </div>\n                  )}\n                </div>\n\n                {/* Targeting Information */}\n                <TargetingDisplay targeting={adSet.targeting_formatted} />\n              </div>\n            ))\n          ) : (\n            <div className=\"no-adsets\">\n              <p>No ad sets found for this ad account.</p>\n            </div>\n          )}\n        </div>\n      )}\n\n      {activeTab === 'ads' && (\n        <div className=\"ads-section\">\n          <h3>Ads</h3>\n          {loading ? (\n            <div className=\"loading\">Loading ads...</div>\n          ) : ads.length > 0 ? (\n            ads.map((ad) => (\n              <div key={ad.id} className=\"ad-item\">\n                <div className=\"ad-header\">\n                  <strong>{ad.name}</strong>\n                  <span className={`status ${ad.status?.toLowerCase()}`}>\n                    {ad.status}\n                  </span>\n                </div>\n                <div className=\"ad-details\">\n                  <div className=\"detail-item\">\n                    <span>Ad Set: {ad.adset_id}</span>\n                  </div>\n                  <div className=\"detail-item\">\n                    <span>Campaign: {ad.campaign_id}</span>\n                  </div>\n                  <div className=\"detail-item\">\n                    <Calendar size={14} />\n                    <span>Created: {new Date(ad.created_time).toLocaleDateString()}</span>\n                  </div>\n                  {ad.insights && ad.insights.impressions && ad.insights.impressions !== '0' && (\n                    <div className=\"detail-item\">\n                      <span>Impressions: {parseInt(ad.insights.impressions).toLocaleString()}</span>\n                    </div>\n                  )}\n                  {ad.insights && ad.insights.clicks && ad.insights.clicks !== '0' && (\n                    <div className=\"detail-item\">\n                      <span>Clicks: {parseInt(ad.insights.clicks).toLocaleString()}</span>\n                    </div>\n                  )}\n                  {ad.insights && ad.insights.spend && ad.insights.spend !== '0.00' && (\n                    <div className=\"detail-item\">\n                      <DollarSign size={14} />\n                      <span>Spend: ${parseFloat(ad.insights.spend).toFixed(2)}</span>\n                    </div>\n                  )}\n                </div>\n\n                {/* Creative Display */}\n                <CreativeDisplay creative={ad.creative_formatted} />\n              </div>\n            ))\n          ) : (\n            <div className=\"no-ads\">\n              <p>No ads found for this ad account.</p>\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* Campaign Wizard Modal */}\n      {showCampaignWizard && (\n        <CampaignWizard\n          adAccounts={adAccounts}\n          selectedAccount={selectedAccount}\n          onClose={() => setShowCampaignWizard(false)}\n          onSuccess={handleWizardSuccess}\n        />\n      )}\n    </div>\n  );\n};\n\nexport default CampaignSection;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,GAAG,IAAIC,WAAW,QAAQ,iBAAiB;AAClD,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,OAAO,QAAQ,iBAAiB;AACzC,OAAOC,KAAK,MAAM,iBAAiB;AACnC,SAASC,MAAM,EAAEC,IAAI,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,GAAG,QAAQ,cAAc;AACnF,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,cAAc,MAAM,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE9C,MAAMC,mBAAmB,GAAG;AAC1B;AACA;EAAEC,KAAK,EAAE,iBAAiB;EAAEC,KAAK,EAAE;AAAU,CAAC,EAC9C;EAAED,KAAK,EAAE,oBAAoB;EAAEC,KAAK,EAAE;AAAa,CAAC,EACpD;EAAED,KAAK,EAAE,eAAe;EAAEC,KAAK,EAAE;AAAkB,CAAC,EACpD;EAAED,KAAK,EAAE,eAAe;EAAEC,KAAK,EAAE;AAAQ,CAAC,EAC1C;EAAED,KAAK,EAAE,mBAAmB;EAAEC,KAAK,EAAE;AAAY,CAAC,EAClD;EAAED,KAAK,EAAE,uBAAuB;EAAEC,KAAK,EAAE;AAAgB,CAAC;AAE1D;AACA;EAAED,KAAK,EAAE,OAAO;EAAEC,KAAK,EAAE;AAAiB,CAAC,EAC3C;EAAED,KAAK,EAAE,aAAa;EAAEC,KAAK,EAAE;AAAuB,CAAC,EACvD;EAAED,KAAK,EAAE,iBAAiB;EAAEC,KAAK,EAAE;AAA2B,CAAC,EAC/D;EAAED,KAAK,EAAE,UAAU;EAAEC,KAAK,EAAE;AAAoB,CAAC,EACjD;EAAED,KAAK,EAAE,aAAa;EAAEC,KAAK,EAAE;AAAuB,CAAC,EACvD;EAAED,KAAK,EAAE,cAAc;EAAEC,KAAK,EAAE;AAAwB,CAAC,CAC1D;AAED,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM;IAAEC;EAAgB,CAAC,GAAGrB,OAAO,CAAC,CAAC;EACrC,MAAM,CAACsB,SAAS,EAAEC,YAAY,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC4B,MAAM,EAAEC,SAAS,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAAC8B,GAAG,EAAEC,MAAM,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAClC,MAAM,CAACgC,UAAU,EAAEC,aAAa,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACkC,OAAO,EAAEC,UAAU,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACoC,cAAc,EAAEC,iBAAiB,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACsC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACwC,eAAe,EAAEC,kBAAkB,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC0C,SAAS,EAAEC,YAAY,CAAC,GAAG3C,QAAQ,CAAC,WAAW,CAAC;EACvD,MAAM,CAAC4C,UAAU,EAAEC,aAAa,CAAC,GAAG7C,QAAQ,CAAC;IAC3C0B,SAAS,EAAE,KAAK;IAChBoB,MAAM,EAAE,KAAK;IACbhB,GAAG,EAAE;EACP,CAAC,CAAC;EAEF,MAAM;IAAEiB,QAAQ;IAAEC,YAAY;IAAEC,KAAK;IAAEC,SAAS,EAAE;MAAEC;IAAO;EAAE,CAAC,GAAG9C,OAAO,CAAC,CAAC;EAE1EJ,SAAS,CAAC,MAAM;IACd,IAAIwB,eAAe,EAAE;MACnB2B,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAAC3B,eAAe,CAAC,CAAC;EAErBxB,SAAS,CAAC,MAAM;IACd,IAAIuC,eAAe,EAAE;MACnB;MACAK,aAAa,CAAC;QACZnB,SAAS,EAAE,KAAK;QAChBoB,MAAM,EAAE,KAAK;QACbhB,GAAG,EAAE;MACP,CAAC,CAAC;MACF;MACAuB,aAAa,CAAC,CAAC;IACjB;EACF,CAAC,EAAE,CAACb,eAAe,CAAC,CAAC;;EAErB;EACAvC,SAAS,CAAC,MAAM;IACd,IAAIuC,eAAe,IAAIE,SAAS,EAAE;MAChC;MACA;MACA,IAAI,CAACE,UAAU,CAAClB,SAAS,EAAE;QACzB2B,aAAa,CAAC,CAAC,CAAC,CAAC;MACnB;IACF;EACF,CAAC,EAAE,CAACX,SAAS,EAAEF,eAAe,EAAEI,UAAU,CAAC,CAAC;EAE5C,MAAMQ,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MAAA,IAAAE,cAAA;MACF;MACA,MAAMC,QAAQ,GAAG,MAAMrD,GAAG,CAACsD,GAAG,CAAC,uBAAuB,CAAC;MACvDvB,aAAa,CAACsB,QAAQ,CAACE,IAAI,IAAI,EAAE,CAAC;MAClC,IAAI,EAAAH,cAAA,GAAAC,QAAQ,CAACE,IAAI,cAAAH,cAAA,uBAAbA,cAAA,CAAeI,MAAM,IAAG,CAAC,EAAE;QAC7BjB,kBAAkB,CAACc,QAAQ,CAACE,IAAI,CAAC,CAAC,CAAC,CAACE,EAAE,CAAC;MACzC;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClDtD,KAAK,CAACsD,KAAK,CAAC,4BAA4B,CAAC;IAC3C;EACF,CAAC;EAED,MAAMP,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI,CAACb,eAAe,EAAE;IAEtB,IAAI;MACFL,UAAU,CAAC,IAAI,CAAC;MAChB0B,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;;MAEtE;MACA,MAAMP,QAAQ,GAAG,MAAMrD,GAAG,CAACsD,GAAG,CAAC,uBAAuBhB,eAAe,EAAE,CAAC;MACxE,MAAMuB,YAAY,GAAGR,QAAQ,CAACE,IAAI,IAAI,EAAE;;MAExC;MACA9B,YAAY,CAACoC,YAAY,CAAC;;MAE1B;MACA,MAAMC,SAAS,GAAGD,YAAY,CAACE,OAAO,CAACC,QAAQ,IAC7C,CAACA,QAAQ,CAACpB,MAAM,IAAI,EAAE,EAAEqB,GAAG,CAACC,KAAK,KAAK;QACpC,GAAGA,KAAK;QACRC,WAAW,EAAEH,QAAQ,CAACP,EAAE;QACxBW,aAAa,EAAEJ,QAAQ,CAACK;MAC1B,CAAC,CAAC,CACJ,CAAC;MACD1C,SAAS,CAACmC,SAAS,CAAC;;MAEpB;MACA,MAAMQ,MAAM,GAAGR,SAAS,CAACC,OAAO,CAACG,KAAK,IACpC,CAACA,KAAK,CAACtC,GAAG,IAAI,EAAE,EAAEqC,GAAG,CAACM,EAAE,KAAK;QAC3B,GAAGA,EAAE;QACLC,QAAQ,EAAEN,KAAK,CAACT,EAAE;QAClBgB,UAAU,EAAEP,KAAK,CAACG,IAAI;QACtBF,WAAW,EAAED,KAAK,CAACC,WAAW;QAC9BC,aAAa,EAAEF,KAAK,CAACE;MACvB,CAAC,CAAC,CACJ,CAAC;MACDvC,MAAM,CAACyC,MAAM,CAAC;;MAEd;MACA3B,aAAa,CAAC;QACZnB,SAAS,EAAE,IAAI;QACfoB,MAAM,EAAE,IAAI;QACZhB,GAAG,EAAE;MACP,CAAC,CAAC;MAEF+B,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE;QACxCpC,SAAS,EAAEqC,YAAY,CAACL,MAAM;QAC9B9B,MAAM,EAAEoC,SAAS,CAACN,MAAM;QACxB5B,GAAG,EAAE0C,MAAM,CAACd;MACd,CAAC,CAAC;IAEJ,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdtD,KAAK,CAACsD,KAAK,CAAC,6BAA6B,CAAC;MAC1CjC,YAAY,CAAC,EAAE,CAAC;MAChBE,SAAS,CAAC,EAAE,CAAC;MACbE,MAAM,CAAC,EAAE,CAAC;IACZ,CAAC,SAAS;MACRI,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;;EAEA,MAAMyC,mBAAmB,GAAIC,MAAM,IAAK;IACtChB,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEe,MAAM,CAAC;IAClDvE,KAAK,CAACwE,OAAO,CAAC,0CAA0C,CAAC;IACzDvC,qBAAqB,CAAC,KAAK,CAAC;IAC5B;IACA,IAAIC,eAAe,EAAE;MACnBa,aAAa,CAACb,eAAe,CAAC;IAChC;EACF,CAAC;EAED,MAAMuC,gBAAgB,GAAG,MAAOtB,IAAI,IAAK;IACvC,IAAI;MACFtB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM6C,YAAY,GAAG;QACnBC,WAAW,EAAEzC,eAAe;QAC5B+B,IAAI,EAAEd,IAAI,CAACc,IAAI;QACfW,SAAS,EAAEzB,IAAI,CAACyB,SAAS;QACzBC,MAAM,EAAE,QAAQ;QAAE;QAClBC,mBAAmB,EAAE3B,IAAI,CAAC2B,mBAAmB,GAAG,CAAC3B,IAAI,CAAC2B,mBAAmB,CAAC,GAAG;MAC/E,CAAC;;MAED;MACA,MAAMlF,GAAG,CAACmF,IAAI,CAAC,qBAAqB,EAAEL,YAAY,CAAC;MACnD1E,KAAK,CAACwE,OAAO,CAAC,gCAAgC,CAAC;;MAE/C;MACA7B,KAAK,CAAC,CAAC;MACPZ,iBAAiB,CAAC,KAAK,CAAC;MACxBgB,aAAa,CAAC,CAAC;IACjB,CAAC,CAAC,OAAOO,KAAK,EAAE;MAAA,IAAA0B,eAAA,EAAAC,oBAAA;MACdjF,KAAK,CAACsD,KAAK,CAAC,EAAA0B,eAAA,GAAA1B,KAAK,CAACL,QAAQ,cAAA+B,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgB7B,IAAI,cAAA8B,oBAAA,uBAApBA,oBAAA,CAAsBC,OAAO,KAAI,2BAA2B,CAAC;IAC3E,CAAC,SAAS;MACRrD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,IAAI,CAACV,eAAe,EAAE;IACpB,oBACER,OAAA;MAAKwE,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BzE,OAAA;QAAAyE,QAAA,EAAI;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC5B7E,OAAA;QAAKwE,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BzE,OAAA,CAACN,WAAW;UAACoF,IAAI,EAAE;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACzB7E,OAAA;UAAAyE,QAAA,EAAG;QAAiC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI9D,UAAU,CAAC0B,MAAM,KAAK,CAAC,EAAE;IAC3B,oBACEzC,OAAA;MAAKwE,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BzE,OAAA;QAAAyE,QAAA,EAAI;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC5B7E,OAAA;QAAKwE,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BzE,OAAA,CAACN,WAAW;UAACoF,IAAI,EAAE;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACzB7E,OAAA;UAAAyE,QAAA,EAAG;QAA0E;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9E,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE7E,OAAA;IAAKwE,SAAS,EAAC,kBAAkB;IAAAC,QAAA,gBAC/BzE,OAAA;MAAAyE,QAAA,EAAI;IAAmB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAE5B7E,OAAA;MAAKwE,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BzE,OAAA;QAAAyE,QAAA,EAAO;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACjC7E,OAAA;QACEI,KAAK,EAAEmB,eAAgB;QACvBwD,QAAQ,EAAGC,CAAC,IAAKxD,kBAAkB,CAACwD,CAAC,CAACC,MAAM,CAAC7E,KAAK,CAAE;QAAAqE,QAAA,EAEnD1D,UAAU,CAACmC,GAAG,CAAEgC,OAAO,iBACtBlF,OAAA;UAAyBI,KAAK,EAAE8E,OAAO,CAACxC,EAAG;UAAA+B,QAAA,GACxCS,OAAO,CAAC5B,IAAI,EAAC,IAAE,EAAC4B,OAAO,CAACxC,EAAE,EAAC,GAC9B;QAAA,GAFawC,OAAO,CAACxC,EAAE;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEf,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAEN7E,OAAA;MAAKwE,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5BzE,OAAA;QACEwE,SAAS,EAAE/C,SAAS,KAAK,WAAW,GAAG,QAAQ,GAAG,EAAG;QACrD0D,OAAO,EAAEA,CAAA,KAAMzD,YAAY,CAAC,WAAW,CAAE;QAAA+C,QAAA,gBAEzCzE,OAAA,CAACV,MAAM;UAACwF,IAAI,EAAE;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACT,EAACpE,SAAS,CAACgC,MAAM,EAAC,GAC/B;MAAA;QAAAiC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT7E,OAAA;QACEwE,SAAS,EAAE/C,SAAS,KAAK,QAAQ,GAAG,QAAQ,GAAG,EAAG;QAClD0D,OAAO,EAAEA,CAAA,KAAMzD,YAAY,CAAC,QAAQ,CAAE;QAAA+C,QAAA,gBAEtCzE,OAAA,CAACR,UAAU;UAACsF,IAAI,EAAE;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,aACf,EAAClE,MAAM,CAAC8B,MAAM,EAAC,GAC1B;MAAA;QAAAiC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT7E,OAAA;QACEwE,SAAS,EAAE/C,SAAS,KAAK,KAAK,GAAG,QAAQ,GAAG,EAAG;QAC/C0D,OAAO,EAAEA,CAAA,KAAMzD,YAAY,CAAC,KAAK,CAAE;QAAA+C,QAAA,gBAEnCzE,OAAA,CAACP,QAAQ;UAACqF,IAAI,EAAE;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,SACjB,EAAChE,GAAG,CAAC4B,MAAM,EAAC,GACnB;MAAA;QAAAiC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAELpD,SAAS,KAAK,WAAW,iBACxBzB,OAAA,CAAAE,SAAA;MAAAuE,QAAA,gBACEzE,OAAA;QAAKwE,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBACnCzE,OAAA;UAAAyE,QAAA,gBACEzE,OAAA,CAACV,MAAM;YAACwF,IAAI,EAAE;UAAG;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACT,EAACpE,SAAS,CAACgC,MAAM,EAAC,GAC/B;QAAA;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL7E,OAAA;UAAKwE,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBzE,OAAA;YACEmF,OAAO,EAAEA,CAAA,KAAM7D,qBAAqB,CAAC,IAAI,CAAE;YAC3CkD,SAAS,EAAC,qDAAqD;YAC/DY,QAAQ,EAAE,CAAC7D,eAAgB;YAAAkD,QAAA,gBAE3BzE,OAAA,CAACL,GAAG;cAACmF,IAAI,EAAE;YAAG;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,mBAEnB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT7E,OAAA;YACEmF,OAAO,EAAEA,CAAA,KAAM/D,iBAAiB,CAAC,CAACD,cAAc,CAAE;YAClDqD,SAAS,EAAC,YAAY;YAAAC,QAAA,gBAEtBzE,OAAA,CAACT,IAAI;cAACuF,IAAI,EAAE;YAAG;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EACjB1D,cAAc,GAAG,QAAQ,GAAG,gBAAgB;UAAA;YAAAuD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAEL1D,cAAc,iBACbnB,OAAA;QAAMqF,QAAQ,EAAEtD,YAAY,CAAC+B,gBAAgB,CAAE;QAACU,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBAC9EzE,OAAA;UAAAyE,QAAA,EAAI;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAE5B7E,OAAA;UAAKwE,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBzE,OAAA;YAAAyE,QAAA,EAAO;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC7B7E,OAAA;YACEsF,IAAI,EAAC,MAAM;YAAA,GACPxD,QAAQ,CAAC,MAAM,EAAE;cAAEyD,QAAQ,EAAE;YAA4B,CAAC,CAAC;YAC/DC,WAAW,EAAC;UAAqB;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,EACD3C,MAAM,CAACoB,IAAI,iBAAItD,OAAA;YAAMwE,SAAS,EAAC,OAAO;YAAAC,QAAA,EAAEvC,MAAM,CAACoB,IAAI,CAACiB;UAAO;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjE,CAAC,eAEN7E,OAAA;UAAKwE,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBzE,OAAA;YAAAyE,QAAA,EAAO;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACzB7E,OAAA;YAAA,GAAY8B,QAAQ,CAAC,WAAW,EAAE;cAAEyD,QAAQ,EAAE;YAAwB,CAAC,CAAC;YAAAd,QAAA,gBACtEzE,OAAA;cAAQI,KAAK,EAAC,EAAE;cAAAqE,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EACzC1E,mBAAmB,CAAC+C,GAAG,CAAEuC,GAAG,iBAC3BzF,OAAA;cAAwBI,KAAK,EAAEqF,GAAG,CAACrF,KAAM;cAAAqE,QAAA,EACtCgB,GAAG,CAACpF;YAAK,GADCoF,GAAG,CAACrF,KAAK;cAAAsE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEd,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,EACR3C,MAAM,CAAC+B,SAAS,iBAAIjE,OAAA;YAAMwE,SAAS,EAAC,OAAO;YAAAC,QAAA,EAAEvC,MAAM,CAAC+B,SAAS,CAACM;UAAO;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3E,CAAC,eAEN7E,OAAA;UAAKwE,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBzE,OAAA;YAAAyE,QAAA,EAAO;UAAiC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAChD7E,OAAA;YAAA,GAAY8B,QAAQ,CAAC,qBAAqB,CAAC;YAAA2C,QAAA,gBACzCzE,OAAA;cAAQI,KAAK,EAAC,EAAE;cAAAqE,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC9B7E,OAAA;cAAQI,KAAK,EAAC,QAAQ;cAAAqE,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACtC7E,OAAA;cAAQI,KAAK,EAAC,YAAY;cAAAqE,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC9C7E,OAAA;cAAQI,KAAK,EAAC,SAAS;cAAAqE,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACxC7E,OAAA;cAAQI,KAAK,EAAC,2BAA2B;cAAAqE,QAAA,EAAC;YAA6B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN7E,OAAA;UAAKwE,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BzE,OAAA;YAAQsF,IAAI,EAAC,QAAQ;YAACF,QAAQ,EAAEnE,OAAQ;YAACuD,SAAS,EAAC,YAAY;YAAAC,QAAA,EAC5DxD,OAAO,GAAG,aAAa,GAAG;UAAiB;YAAAyD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,eACT7E,OAAA;YACEsF,IAAI,EAAC,QAAQ;YACbH,OAAO,EAAEA,CAAA,KAAM/D,iBAAiB,CAAC,KAAK,CAAE;YACxCoD,SAAS,EAAC,YAAY;YAAAC,QAAA,EACvB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CACP,eAED7E,OAAA;QAAKwE,SAAS,EAAC,gBAAgB;QAAAC,QAAA,EAC5BxD,OAAO,gBACNjB,OAAA;UAAKwE,SAAS,EAAC,SAAS;UAAAC,QAAA,EAAC;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,GACjDpE,SAAS,CAACgC,MAAM,GAAG,CAAC,GACtBhC,SAAS,CAACyC,GAAG,CAAED,QAAQ;UAAA,IAAAyC,gBAAA;UAAA,oBACrB1F,OAAA;YAAuBwE,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC9CzE,OAAA;cAAKwE,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9BzE,OAAA;gBAAAyE,QAAA,EAAKxB,QAAQ,CAACK;cAAI;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACxB7E,OAAA;gBAAMwE,SAAS,EAAE,oBAAAkB,gBAAA,GAAmBzC,QAAQ,CAACiB,MAAM,cAAAwB,gBAAA,uBAAfA,gBAAA,CAAiBC,WAAW,CAAC,CAAC,EAAG;gBAAAlB,QAAA,EAClExB,QAAQ,CAACiB;cAAM;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACN7E,OAAA;cAAKwE,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/BzE,OAAA;gBAAKwE,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BzE,OAAA,CAACV,MAAM;kBAACwF,IAAI,EAAE;gBAAG;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACpB7E,OAAA;kBAAAyE,QAAA,GAAM,aAAW,EAACxB,QAAQ,CAACgB,SAAS;gBAAA;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC,CAAC,eACN7E,OAAA;gBAAKwE,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BzE,OAAA,CAACP,QAAQ;kBAACqF,IAAI,EAAE;gBAAG;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACtB7E,OAAA;kBAAAyE,QAAA,GAAM,WAAS,EAAC,IAAImB,IAAI,CAAC3C,QAAQ,CAAC4C,YAAY,CAAC,CAACC,kBAAkB,CAAC,CAAC;gBAAA;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzE,CAAC,EACL5B,QAAQ,CAAC8C,YAAY,iBACpB/F,OAAA;gBAAKwE,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BzE,OAAA,CAACR,UAAU;kBAACsF,IAAI,EAAE;gBAAG;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACxB7E,OAAA;kBAAAyE,QAAA,GAAM,iBAAe,EAAC,CAACxB,QAAQ,CAAC8C,YAAY,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC;gBAAA;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnE,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA,GAtBE5B,QAAQ,CAACP,EAAE;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAuBhB,CAAC;QAAA,CACP,CAAC,gBAEF7E,OAAA;UAAKwE,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BzE,OAAA;YAAAyE,QAAA,EAAG;UAAuC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC9C7E,OAAA;YAAAyE,QAAA,EAAG;UAAgD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA,eACF,CACH,EAEApD,SAAS,KAAK,QAAQ,iBACrBzB,OAAA;MAAKwE,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BzE,OAAA;QAAAyE,QAAA,EAAI;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EACf5D,OAAO,gBACNjB,OAAA;QAAKwE,SAAS,EAAC,SAAS;QAAAC,QAAA,EAAC;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,GAC/ClE,MAAM,CAAC8B,MAAM,GAAG,CAAC,GACnB9B,MAAM,CAACuC,GAAG,CAAE+C,KAAK;QAAA,IAAAC,aAAA;QAAA,oBACflG,OAAA;UAAoBwE,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACxCzE,OAAA;YAAKwE,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BzE,OAAA;cAAAyE,QAAA,EAASwB,KAAK,CAAC3C;YAAI;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,eAC7B7E,OAAA;cAAMwE,SAAS,EAAE,WAAA0B,aAAA,GAAUD,KAAK,CAAC/B,MAAM,cAAAgC,aAAA,uBAAZA,aAAA,CAAcP,WAAW,CAAC,CAAC,EAAG;cAAAlB,QAAA,EACtDwB,KAAK,CAAC/B;YAAM;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACN7E,OAAA;YAAKwE,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BzE,OAAA;cAAKwE,SAAS,EAAC,aAAa;cAAAC,QAAA,eAC1BzE,OAAA;gBAAAyE,QAAA,GAAM,YAAU,EAACwB,KAAK,CAAC7C,WAAW;cAAA;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC,eACN7E,OAAA;cAAKwE,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BzE,OAAA,CAACP,QAAQ;gBAACqF,IAAI,EAAE;cAAG;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACtB7E,OAAA;gBAAAyE,QAAA,GAAM,WAAS,EAAC,IAAImB,IAAI,CAACK,KAAK,CAACJ,YAAY,CAAC,CAACC,kBAAkB,CAAC,CAAC;cAAA;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE,CAAC,EACLoB,KAAK,CAACF,YAAY,iBACjB/F,OAAA;cAAKwE,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BzE,OAAA,CAACR,UAAU;gBAACsF,IAAI,EAAE;cAAG;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACxB7E,OAAA;gBAAAyE,QAAA,GAAM,iBAAe,EAAC,CAACwB,KAAK,CAACF,YAAY,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC;cAAA;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CACN,EACAoB,KAAK,CAACE,iBAAiB,iBACtBnG,OAAA;cAAKwE,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BzE,OAAA,CAACV,MAAM;gBAACwF,IAAI,EAAE;cAAG;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACpB7E,OAAA;gBAAAyE,QAAA,GAAM,gBAAc,EAACwB,KAAK,CAACE,iBAAiB;cAAA;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGN7E,OAAA,CAACJ,gBAAgB;YAACwG,SAAS,EAAEH,KAAK,CAACI;UAAoB;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA,GA9BlDoB,KAAK,CAACvD,EAAE;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA+Bb,CAAC;MAAA,CACP,CAAC,gBAEF7E,OAAA;QAAKwE,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxBzE,OAAA;UAAAyE,QAAA,EAAG;QAAqC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN,EAEApD,SAAS,KAAK,KAAK,iBAClBzB,OAAA;MAAKwE,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BzE,OAAA;QAAAyE,QAAA,EAAI;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EACX5D,OAAO,gBACNjB,OAAA;QAAKwE,SAAS,EAAC,SAAS;QAAAC,QAAA,EAAC;MAAc;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,GAC3ChE,GAAG,CAAC4B,MAAM,GAAG,CAAC,GAChB5B,GAAG,CAACqC,GAAG,CAAEM,EAAE;QAAA,IAAA8C,UAAA;QAAA,oBACTtG,OAAA;UAAiBwE,SAAS,EAAC,SAAS;UAAAC,QAAA,gBAClCzE,OAAA;YAAKwE,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBzE,OAAA;cAAAyE,QAAA,EAASjB,EAAE,CAACF;YAAI;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,eAC1B7E,OAAA;cAAMwE,SAAS,EAAE,WAAA8B,UAAA,GAAU9C,EAAE,CAACU,MAAM,cAAAoC,UAAA,uBAATA,UAAA,CAAWX,WAAW,CAAC,CAAC,EAAG;cAAAlB,QAAA,EACnDjB,EAAE,CAACU;YAAM;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACN7E,OAAA;YAAKwE,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBzE,OAAA;cAAKwE,SAAS,EAAC,aAAa;cAAAC,QAAA,eAC1BzE,OAAA;gBAAAyE,QAAA,GAAM,UAAQ,EAACjB,EAAE,CAACC,QAAQ;cAAA;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC,eACN7E,OAAA;cAAKwE,SAAS,EAAC,aAAa;cAAAC,QAAA,eAC1BzE,OAAA;gBAAAyE,QAAA,GAAM,YAAU,EAACjB,EAAE,CAACJ,WAAW;cAAA;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC,eACN7E,OAAA;cAAKwE,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BzE,OAAA,CAACP,QAAQ;gBAACqF,IAAI,EAAE;cAAG;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACtB7E,OAAA;gBAAAyE,QAAA,GAAM,WAAS,EAAC,IAAImB,IAAI,CAACpC,EAAE,CAACqC,YAAY,CAAC,CAACC,kBAAkB,CAAC,CAAC;cAAA;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnE,CAAC,EACLrB,EAAE,CAAC+C,QAAQ,IAAI/C,EAAE,CAAC+C,QAAQ,CAACC,WAAW,IAAIhD,EAAE,CAAC+C,QAAQ,CAACC,WAAW,KAAK,GAAG,iBACxExG,OAAA;cAAKwE,SAAS,EAAC,aAAa;cAAAC,QAAA,eAC1BzE,OAAA;gBAAAyE,QAAA,GAAM,eAAa,EAACgC,QAAQ,CAACjD,EAAE,CAAC+C,QAAQ,CAACC,WAAW,CAAC,CAACE,cAAc,CAAC,CAAC;cAAA;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3E,CACN,EACArB,EAAE,CAAC+C,QAAQ,IAAI/C,EAAE,CAAC+C,QAAQ,CAACI,MAAM,IAAInD,EAAE,CAAC+C,QAAQ,CAACI,MAAM,KAAK,GAAG,iBAC9D3G,OAAA;cAAKwE,SAAS,EAAC,aAAa;cAAAC,QAAA,eAC1BzE,OAAA;gBAAAyE,QAAA,GAAM,UAAQ,EAACgC,QAAQ,CAACjD,EAAE,CAAC+C,QAAQ,CAACI,MAAM,CAAC,CAACD,cAAc,CAAC,CAAC;cAAA;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjE,CACN,EACArB,EAAE,CAAC+C,QAAQ,IAAI/C,EAAE,CAAC+C,QAAQ,CAACK,KAAK,IAAIpD,EAAE,CAAC+C,QAAQ,CAACK,KAAK,KAAK,MAAM,iBAC/D5G,OAAA;cAAKwE,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BzE,OAAA,CAACR,UAAU;gBAACsF,IAAI,EAAE;cAAG;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACxB7E,OAAA;gBAAAyE,QAAA,GAAM,UAAQ,EAACoC,UAAU,CAACrD,EAAE,CAAC+C,QAAQ,CAACK,KAAK,CAAC,CAACZ,OAAO,CAAC,CAAC,CAAC;cAAA;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGN7E,OAAA,CAACH,eAAe;YAACiH,QAAQ,EAAEtD,EAAE,CAACuD;UAAmB;YAAArC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA,GArC5CrB,EAAE,CAACd,EAAE;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAsCV,CAAC;MAAA,CACP,CAAC,gBAEF7E,OAAA;QAAKwE,SAAS,EAAC,QAAQ;QAAAC,QAAA,eACrBzE,OAAA;UAAAyE,QAAA,EAAG;QAAiC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN,EAGAxD,kBAAkB,iBACjBrB,OAAA,CAACF,cAAc;MACbiB,UAAU,EAAEA,UAAW;MACvBQ,eAAe,EAAEA,eAAgB;MACjCyF,OAAO,EAAEA,CAAA,KAAM1F,qBAAqB,CAAC,KAAK,CAAE;MAC5C2F,SAAS,EAAEtD;IAAoB;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChC,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACtE,EAAA,CA3cID,eAAe;EAAA,QACSnB,OAAO,EAgB8BC,OAAO;AAAA;AAAA8H,EAAA,GAjBpE5G,eAAe;AA6crB,eAAeA,eAAe;AAAC,IAAA4G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}