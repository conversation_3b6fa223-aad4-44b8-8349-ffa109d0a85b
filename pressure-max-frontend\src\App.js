import React from 'react';
import { Toaster } from 'react-hot-toast';
import { AuthProvider } from './contexts/AuthContext';
import { FacebookProvider } from './contexts/FacebookContext';
import AuthSection from './components/AuthSection';
import FacebookConnectionSection from './components/FacebookConnectionSection';
import CampaignSection from './components/CampaignSection';
import LeadFormsSection from './components/LeadFormsSection';
import ApiTestingSection from './components/ApiTestingSection';
import './App.css';

function App() {
  return (
    <AuthProvider>
      <FacebookProvider>
        <div className="App">
        <header className="app-header">
          <h1>🚀 Pressure Max API Tester</h1>
          <p>Test interface for the Pressure Max API backend</p>
        </header>

        <main className="app-main">
          <div className="sections-container">
            <section className="section">
              <AuthSection />
            </section>

            <section className="section">
              <FacebookConnectionSection />
            </section>

            <section className="section">
              <CampaignSection />
            </section>

            <section className="section">
              <LeadFormsSection />
            </section>

            <section className="section">
              <ApiTestingSection />
            </section>
          </div>
        </main>

        <footer className="app-footer">
          <p>Pressure Max API Testing Interface - Built with React</p>
          <p>Backend API: <code>http://localhost:3000</code></p>
        </footer>

        <Toaster 
          position="top-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: '#363636',
              color: '#fff',
            },
            success: {
              duration: 3000,
              iconTheme: {
                primary: '#4ade80',
                secondary: '#fff',
              },
            },
            error: {
              duration: 5000,
              iconTheme: {
                primary: '#ef4444',
                secondary: '#fff',
              },
            },
          }}
        />
        </div>
      </FacebookProvider>
    </AuthProvider>
  );
}

export default App;
